import request from '@/utils/request'

/**
 * 仓库管理相关API接口
 */

// ==================== 收货管理 ====================

/**
 * 获取收货任务列表
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.status 任务状态 (pending: 待收货, completed: 已收货)
 * @param {string} params.batchNumber 批次编号
 * @param {string} params.purchaseOrderNo 采购订单号
 * @returns {Promise} 返回任务列表数据
 */
export function getReceivingTaskList(params) {
  return request({
    url: '/warehouse/receiving/task/list',
    method: 'GET',
    params
  })
}

/**
 * 获取收货任务详情
 * @param {string} taskId 任务ID
 * @returns {Promise} 返回任务详情数据
 */
export function getReceivingDetail(taskId) {
  return request({
    url: `/warehouse/receiving/detail/${taskId}`,
    method: 'GET'
  })
}

/**
 * 更新货品收货状态
 * @param {Object} data 更新数据
 * @param {string} data.taskId 任务ID
 * @param {string} data.goodsCode 货品编码
 * @param {string} data.status 状态 (received: 已收货, pending: 待收货)
 * @param {number} data.quantity 收货数量
 * @param {string} data.operator 操作人
 * @returns {Promise} 返回更新结果
 */
export function updateGoodsReceivingStatus(data) {
  return request({
    url: '/warehouse/receiving/goods/status',
    method: 'PUT',
    data
  })
}

/**
 * 批量更新收货状态
 * @param {Object} data 批量更新数据
 * @param {string} data.taskId 任务ID
 * @param {Array} data.goodsList 货品列表
 * @param {string} data.operator 操作人
 * @returns {Promise} 返回更新结果
 */
export function batchUpdateReceivingStatus(data) {
  return request({
    url: '/warehouse/receiving/batch/update',
    method: 'PUT',
    data
  })
}

/**
 * 完成收货任务
 * @param {string} taskId 任务ID
 * @param {Object} data 完成数据
 * @param {string} data.operator 操作人
 * @param {string} data.remark 备注
 * @returns {Promise} 返回完成结果
 */
export function completeReceivingTask(taskId, data) {
  return request({
    url: `/warehouse/receiving/complete/${taskId}`,
    method: 'PUT',
    data
  })
}

// ==================== 上架管理 ====================

/**
 * 获取上架任务列表
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.status 任务状态
 * @returns {Promise} 返回上架任务列表
 */
export function getShelvingTaskList(params) {
  return request({
    url: '/warehouse/shelving/task/list',
    method: 'GET',
    params
  })
}

/**
 * 获取上架任务详情
 * @param {string} taskId 任务ID
 * @returns {Promise} 返回上架任务详情
 */
export function getShelvingDetail(taskId) {
  return request({
    url: `/warehouse/shelving/detail/${taskId}`,
    method: 'GET'
  })
}

/**
 * 更新上架状态
 * @param {Object} data 上架数据
 * @param {string} data.taskId 任务ID
 * @param {string} data.goodsCode 货品编码
 * @param {string} data.locationCode 货位编码
 * @param {number} data.quantity 上架数量
 * @returns {Promise} 返回上架结果
 */
export function updateShelvingStatus(data) {
  return request({
    url: '/warehouse/shelving/update',
    method: 'PUT',
    data
  })
}

// ==================== 库存管理 ====================

/**
 * 查询库存信息
 * @param {Object} params 查询参数
 * @param {string} params.goodsCode 货品编码
 * @param {string} params.goodsName 货品名称
 * @param {string} params.locationCode 货位编码
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页数量
 * @returns {Promise} 返回库存信息
 */
export function getInventoryList(params) {
  return request({
    url: '/warehouse/inventory/list',
    method: 'GET',
    params
  })
}

/**
 * 获取货品库存详情
 * @param {string} goodsCode 货品编码
 * @returns {Promise} 返回货品库存详情
 */
export function getGoodsInventory(goodsCode) {
  return request({
    url: `/warehouse/inventory/goods/${goodsCode}`,
    method: 'GET'
  })
}

// ==================== 扫码功能 ====================

/**
 * 根据扫码结果获取货品信息并验证是否属于指定批次
 * @param {string} code 扫码结果
 * @param {string} type 扫码类型 (goods: 货品码, location: 货位码, batch: 批次码)
 * @param {string} batchNumber 批次编号（可选，用于验证是否属于当前批次）
 * @returns {Promise} 返回货品信息和批次验证结果
 * 返回数据格式：{
 *   code: 200,
 *   data: {
 *     goodsCode: '货品编码',
 *     goodsName: '货品名称',
 *     belongsToBatch: true/false, // 是否属于指定批次
 *     specification: '规格',
 *     unit: '单位',
 *     multipleGoods: [ // 当存在一对多关系时返回此字段
 *       {
 *         goodsCode: '商品编码1',
 *         goodsName: '商品名称1'
 *       },
 *       {
 *         goodsCode: '商品编码2',
 *         goodsName: '商品名称2'
 *       }
 *     ]
 *   }
 * }
 */
export function getScanResult(code, type, batchNumber = '') {
  return request({
    url: '/warehouse/scan/result',
    method: 'GET',
    params: { code, type, batchNumber }
  })
}

/**
 * 验证货品条码是否属于指定批次
 * @param {Object} params 验证参数
 * @param {string} params.taskId 任务ID
 * @param {string} params.batchNumber 批次编号
 * @param {string} params.goodsCode 货品条码
 * @returns {Promise} 返回验证结果
 */
export function validateGoodsCode(params) {
  return request({
    url: '/warehouse/receiving/validate/goods',
    method: 'POST',
    data: params
  })
}

/**
 * 获取批次下的货品列表（用于手动选择）
 * @param {Object} params 查询参数
 * @param {string} params.taskId 任务ID
 * @param {string} params.batchNumber 批次编号
 * @param {string} params.keyword 搜索关键词（可选）
 * @returns {Promise} 返回货品列表
 */
export function getBatchGoodsList(params) {
  return request({
    url: '/warehouse/receiving/batch/goods',
    method: 'GET',
    params
  })
}

/**
 * 记录扫码收货操作
 * @param {Object} data 收货数据
 * @param {string} data.taskId 任务ID
 * @param {string} data.goodsCode 货品条码
 * @param {number} data.scannedQuantity 扫码数量
 * @param {string} data.operator 操作人
 * @param {string} data.scanTime 扫码时间
 * @returns {Promise} 返回操作结果
 */
export function recordScanReceiving(data) {
  return request({
    url: '/warehouse/receiving/scan/record',
    method: 'POST',
    data
  })
}

// ==================== 统计报表 ====================

/**
 * 获取仓库作业统计
 * @param {Object} params 查询参数
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @param {string} params.type 统计类型
 * @returns {Promise} 返回统计数据
 */
export function getWarehouseStatistics(params) {
  return request({
    url: '/warehouse/statistics',
    method: 'GET',
    params
  })
}
