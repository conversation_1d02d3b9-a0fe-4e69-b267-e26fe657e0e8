<template>
  <view class="container">
    <!-- 收货信息部分 -->
    <view class="info-section">
      <view class="section-title">收货信息</view>

      <view class="info-item">
        <text class="info-label">批次编号</text>
        <text class="info-value">{{ receivingInfo.batchNumber }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">采购编号</text>
        <text class="info-value">{{ receivingInfo.purchaseNumber }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">货权主体</text>
        <text class="info-value">{{ receivingInfo.cargoOwner }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">供应商</text>
        <text class="info-value">{{ receivingInfo.supplier }}</text>
      </view>
    </view>

    <!-- 货品信息部分 -->
    <view class="goods-section">
      <view class="section-header">
        <text class="section-title">货品信息</text>
        <view class="status-summary">
          <text class="status-item received">已收货：{{ receivedCount }}</text>
          <text class="status-item pending">待收货：{{ pendingCount }}</text>
        </view>
      </view>

      <!-- 货品列表表格 -->
      <scroll-view class="table-container" scroll-x="true" show-scrollbar="true">
        <view class="goods-table">
          <!-- 表头 -->
          <view class="table-header">
            <text class="col col-code">货品编码</text>
            <text class="col col-name">货品名称</text>
            <text class="col col-spec">规格</text>
            <text class="col col-qty">数量</text>
            <text class="col col-brand">品牌</text>
            <text class="col col-unit">单位</text>
            <text class="col col-mark">赋码</text>
            <text class="col col-status">状态</text>
          </view>

          <!-- 表格内容 -->
          <scroll-view class="table-body" scroll-y="true">
            <view
              v-for="(item, index) in goodsList"
              :key="index"
              class="table-row"
            >
              <text class="col col-code">{{ item.goodsCode }}</text>
              <text class="col col-name">{{ item.goodsName }}</text>
              <text class="col col-spec">{{ item.specification }}</text>
              <text class="col col-qty">{{ item.quantity }}</text>
              <text class="col col-brand">{{ item.brand }}</text>
              <text class="col col-unit">{{ item.unit }}</text>
              <text class="col col-mark">{{ item.mark }}</text>
              <text class="col col-status" :class="getStatusClass(item.status)">
                {{ getStatusText(item.status) }}
              </text>
            </view>
          </scroll-view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <!-- 暂存模式下的按钮 -->
      <view v-if="mode === 'temporary'" class="temporary-actions">
        <button class="btn-cancel" @click="cancelTemporary">取消</button>
        <button class="btn-complete" @click="completeTemporary">完成暂存</button>
      </view>
      <!-- 普通模式下的返回按钮 -->
      <button v-else class="btn-return" @click="goBack">返回</button>
    </view>
  </view>
</template>

<script>
import { getReceivingDetail, updateGoodsReceivingStatus } from '@/api/warehouse'

export default {
  data() {
    return {
      // 收货基本信息
      receivingInfo: {
        batchNumber: '202506231256',
        purchaseNumber: 'CG202506231256',
        cargoOwner: '合福酒业',
        supplier: '供应商1'
      },

      // 货品列表数据
      goodsList: [
        {
          goodsCode: '01010101001',
          goodsName: '52度剑南春1号酒1650ml',
          specification: '1650ml',
          quantity: 3,
          brand: '剑南春',
          unit: '瓶',
          mark: '否',
          status: 'received' // 已收货
        },
        {
          goodsCode: '01010101002',
          goodsName: '52度五粮液161号500ml',
          specification: '500ml',
          quantity: 6,
          brand: '五粮液',
          unit: '瓶',
          mark: '否',
          status: 'received' // 已收货
        },
        {
          goodsCode: '01010101003',
          goodsName: '52度剑南春1号酒1650ml',
          specification: '1650ml',
          quantity: 1,
          brand: '剑南春',
          unit: '瓶',
          mark: '否',
          status: 'pending' // 待收货
        },
        {
          goodsCode: '01010101004',
          goodsName: '52度五粮液161号500ml',
          specification: '500ml',
          quantity: 2,
          brand: '五粮液',
          unit: '瓶',
          mark: '',
          status: 'pending' // 待收货
        }
      ],

      // 页面参数
      taskId: '',
      mode: 'view', // view: 查看模式, receiving: 收货模式
      selectedGoodsCode: '', // 从扫码页面传来的货品编码
      fromSource: '' // 来源：manual(手动选择) 或 scan(扫码)
    }
  },

  computed: {
    // 计算已收货数量
    receivedCount() {
      return this.goodsList.filter(item => item.status === 'received').length
    },

    // 计算待收货数量
    pendingCount() {
      return this.goodsList.filter(item => item.status === 'pending').length
    }
  },

  onLoad(options) {
    // 获取页面参数
    this.taskId = options.taskId || ''
    this.mode = options.mode || 'view'
    this.selectedGoodsCode = options.goodsCode || '' // 从扫码页面传来的货品编码
    this.fromSource = options.from || '' // 来源：manual(手动选择) 或 scan(扫码)

    console.log('收货详情页面参数:', {
      taskId: this.taskId,
      mode: this.mode,
      goodsCode: this.selectedGoodsCode,
      from: this.fromSource
    })

    // 如果是暂存模式，设置页面标题
    if (this.mode === 'temporary') {
      uni.setNavigationBarTitle({
        title: '收货作业'
      })
    }

    // 加载收货详情数据
    this.loadReceivingDetail()
  },

  methods: {
    /**
     * 【后端接口】加载收货详情数据
     * 接口地址：GET /warehouse/receiving/detail/{taskId}
     * 请求参数：taskId - 任务ID
     * 返回数据：{
     *   code: 200,
     *   data: {
     *     receivingInfo: { 收货基本信息 },
     *     goodsList: [ 货品列表 ]
     *   }
     * }
     */
    async loadReceivingDetail() {
      try {
        uni.showLoading({ title: '加载中...' })

        // 【真实接口调用】
        const response = await getReceivingDetail(this.taskId)
        if (response.code === 200) {
          this.receivingInfo = response.data.receivingInfo
          this.goodsList = response.data.goodsList
        } else {
          throw new Error(response.msg || '获取数据失败')
        }



      } catch (error) {
        console.error('加载收货详情失败:', error)

        // 如果是开发环境或接口未实现，使用模拟数据
        if (process.env.NODE_ENV === 'development' || error.message.includes('404')) {
          // 当前已有模拟数据，无需额外处理
        } else {
          uni.showToast({
            title: '加载失败',
            icon: 'error'
          })
        }
      } finally {
        uni.hideLoading()
      }
    },

    /**
     * 【后端接口】更新货品收货状态
     * 接口地址：PUT /warehouse/receiving/goods/status
     * 请求参数：{
     *   taskId: '任务ID',
     *   goodsCode: '货品编码',
     *   status: '状态',
     *   quantity: '数量'
     * }
     */
    async updateGoodsStatus(goodsCode, status, quantity) {
      try {
        const response = await updateGoodsReceivingStatus({
          taskId: this.taskId,
          goodsCode: goodsCode,
          status: status,
          quantity: quantity,
          operator: uni.getStorageSync('userInfo')?.userName || 'system'
        })

        if (response.code === 200) {
          // 更新本地数据
          const goodsItem = this.goodsList.find(item => item.goodsCode === goodsCode)
          if (goodsItem) {
            goodsItem.status = status
            goodsItem.quantity = quantity
          }

          uni.showToast({
            title: '更新成功',
            icon: 'success'
          })
        } else {
          throw new Error(response.msg || '更新失败')
        }



      } catch (error) {
        console.error('更新货品状态失败:', error)
        uni.showToast({
          title: '更新失败',
          icon: 'error'
        })
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'received': '已收货',
        'pending': '待收货'
      }
      return statusMap[status] || '待收货'
    },

    // 获取状态样式类名
    getStatusClass(status) {
      return `status-${status}`
    },

    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 取消暂存
    cancelTemporary() {
      uni.navigateBack()
    },

    // 【后端接口】完成暂存
    async completeTemporary() {
      try {
        // 【接口地址】POST /warehouse/shelving/task/{taskId}/temporary-complete
        // 【请求参数】taskId: string
        // 【返回数据】{ success: boolean, message: string }
        // await this.$api.completeTemporaryStorage(this.taskId)

        uni.showToast({
          title: '暂存完成',
          icon: 'success'
        })

        // 返回上架任务列表页面
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/warehouse/shelving/task-list'
          })
        }, 1500)

        console.log('完成暂存任务:', this.taskId)
      } catch (error) {
        console.error('完成暂存失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 120rpx; // 为底部按钮留出空间
}

/* 信息区域通用样式 */
.info-section, .goods-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 收货信息部分 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

/* 货品信息部分 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.status-summary {
  display: flex;
  gap: 24rpx;
}

.status-item {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;

  &.received {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1rpx solid #b7eb8f;
  }

  &.pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1rpx solid #ffd591;
  }
}

/* 表格容器样式 */
.table-container {
  width: 100%;
  white-space: nowrap;
  /* 显示滚动条 */
  ::-webkit-scrollbar {
    height: 8rpx;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4rpx;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4rpx;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

/* 货品表格样式 */
.goods-table {
  border: 1rpx solid #e8e8e8;
  border-radius: 8rpx;
  overflow: hidden;
  min-width: 1000rpx; /* 确保表格有足够的宽度显示所有列 */
  display: inline-block;
}

.table-header, .table-row {
  display: flex;
  align-items: center;
  min-height: 80rpx;
}

.table-header {
  background-color: #fafafa;
  border-bottom: 1rpx solid #e8e8e8;
  font-weight: 600;
  color: #333;
}

.table-row {
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &:nth-child(even) {
    background-color: #fafafa;
  }
}

.table-body {
  max-height: 600rpx;
}

/* 表格列宽设置 */
.col {
  padding: 16rpx 8rpx;
  font-size: 24rpx;
  text-align: center;
  word-break: break-all;
  line-height: 1.4;

  &.col-code {
    width: 180rpx;
    flex-shrink: 0;
    text-align: left;
  }

  &.col-name {
    width: 240rpx;
    flex-shrink: 0;
    text-align: left;
  }

  &.col-spec {
    width: 120rpx;
    flex-shrink: 0;
  }

  &.col-qty {
    width: 80rpx;
    flex-shrink: 0;
  }

  &.col-brand {
    width: 120rpx;
    flex-shrink: 0;
  }

  &.col-unit {
    width: 80rpx;
    flex-shrink: 0;
  }

  &.col-mark {
    width: 80rpx;
    flex-shrink: 0;
  }

  &.col-status {
    width: 120rpx;
    flex-shrink: 0;
    font-weight: 500;
  }
}

/* 状态样式 */
.status-received {
  color: #52c41a;
}

.status-pending {
  color: #fa8c16;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #e8e8e8;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.btn-return {
  width: 100%;
  height: 88rpx;
  background-color: #909399;
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    background-color: #82848a;
  }
}

/* 暂存模式下的按钮组 */
.temporary-actions {
  display: flex;
  gap: 24rpx;
}

.btn-cancel, .btn-complete {
  flex: 1;
  height: 88rpx;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-cancel {
  background-color: #909399;
  color: #fff;

  &:active {
    background-color: #82848a;
  }
}

.btn-complete {
  background-color: #52c41a;
  color: #fff;

  &:active {
    background-color: #389e0d;
  }
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .col {
    font-size: 22rpx;
    padding: 12rpx 6rpx;
  }

  .col-code {
    width: 160rpx;
  }

  .col-name {
    width: 200rpx;
  }

  .col-spec {
    width: 100rpx;
  }

  .col-qty {
    width: 60rpx;
  }

  .col-brand {
    width: 100rpx;
  }

  .col-unit {
    width: 60rpx;
  }

  .col-mark {
    width: 60rpx;
  }

  .col-status {
    width: 100rpx;
  }
}
</style>