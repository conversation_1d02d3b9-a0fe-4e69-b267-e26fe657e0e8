<template>
  <view class="container">
    <view class="header">
      <text class="title">批次状态测试页面</text>
    </view>
    
    <view class="test-section">
      <view class="section-title">测试不同批次状态的上架页面显示</view>
      
      <view class="test-buttons">
        <button class="test-btn btn-current-batch" @click="testCurrentBatch">
          测试属于当前批次
        </button>
        <button class="test-btn btn-other-batch" @click="testOtherBatch">
          测试不属于当前批次
        </button>
      </view>
      
      <view class="description">
        <view class="desc-item">
          <text class="desc-title">属于当前批次时：</text>
          <text class="desc-content">• 货品编码、名称、规格为只读显示</text>
          <text class="desc-content">• 收货详情中显示物流码和唯一码</text>
          <text class="desc-content">• 底部没有"继续收货"按钮</text>
        </view>
        
        <view class="desc-item">
          <text class="desc-title">不属于当前批次时：</text>
          <text class="desc-content">• 货品编码、名称、规格变为可输入框</text>
          <text class="desc-content">• 收货详情中只显示物流码，不显示唯一码</text>
          <text class="desc-content">• 底部显示"继续收货"按钮</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      taskId: 'TEST_TASK_001'
    }
  },
  
  methods: {
    // 测试属于当前批次的情况
    testCurrentBatch() {
      uni.navigateTo({
        url: `/pages/warehouse/shelving/shelving-detail?taskId=${this.taskId}&goodsCode=TEST001&mode=shelving&belongsToCurrentBatch=true`
      })
    },
    
    // 测试不属于当前批次的情况
    testOtherBatch() {
      uni.navigateTo({
        url: `/pages/warehouse/shelving/shelving-detail?taskId=${this.taskId}&goodsCode=TEST002&mode=shelving&belongsToCurrentBatch=false&from=overReceive`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 32rpx;
}

.header {
  text-align: center;
  margin-bottom: 48rpx;
  
  .title {
    font-size: 40rpx;
    font-weight: 600;
    color: #333;
  }
}

.test-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 32rpx;
    text-align: center;
  }
  
  .test-buttons {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    margin-bottom: 48rpx;
    
    .test-btn {
      height: 80rpx;
      border: none;
      border-radius: 8rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #fff;
      
      &.btn-current-batch {
        background-color: #52c41a;
      }
      
      &.btn-other-batch {
        background-color: #fa8c16;
      }
    }
  }
  
  .description {
    .desc-item {
      margin-bottom: 32rpx;
      
      .desc-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        display: block;
        margin-bottom: 16rpx;
      }
      
      .desc-content {
        font-size: 26rpx;
        color: #666;
        display: block;
        margin-bottom: 8rpx;
        padding-left: 16rpx;
      }
    }
  }
}
</style>
