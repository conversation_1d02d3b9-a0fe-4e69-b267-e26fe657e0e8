<template>
  <view class="container">
    <!-- 顶部标签栏 -->
    <view class="tab-bar">
      <view
        class="tab-item"
        :class="{ active: currentStatus === '' }"
        @click="switchTab('')"
      >
        全部
      </view>
      <view
        class="tab-item"
        :class="{ active: currentStatus === 'pending' }"
        @click="switchTab('pending')"
      >
        待上架
      </view>
      <view
        class="tab-item"
        :class="{ active: currentStatus === 'temporary' }"
        @click="switchTab('temporary')"
      >
        待暂存
      </view>
      <view
        class="tab-item"
        :class="{ active: currentStatus === 'stored' }"
        @click="switchTab('stored')"
      >
        已暂存
      </view>
      <view
        class="tab-item"
        :class="{ active: currentStatus === 'completed' }"
        @click="switchTab('completed')"
      >
        已上架
      </view>
    </view>

    <!-- 任务列表 -->
    <view class="task-list">
      <view 
        v-for="(task, index) in taskList" 
        :key="task.id"
        class="task-item"
        @click="goToShelvingDetail(task)"
      >
        <!-- 任务基本信息 -->
        <view class="task-header">
          <view class="task-title">批次编号</view>
          <view class="task-number">{{ task.batchNumber }}</view>
        </view>

        <!-- 分隔线 -->
        <view class="divider-line"></view>

        <view class="task-content">
          <view class="task-info">
            <view class="info-row full-width">
              <text class="label">采购单号</text>
              <text class="value">{{ task.purchaseOrderNo }}</text>
            </view>
            <view class="info-row full-width">
              <text class="label">推荐库位</text>
              <text class="value">{{ task.stockQuantity }}</text>
            </view>
            <view class="info-row full-width">
              <text class="label">货存主体</text>
              <text class="value">{{ task.stockEntity }}</text>
            </view>
            <view class="info-row full-width">
              <text class="label">品类数量</text>
              <text class="value">{{ task.shelvingQuantity }}</text>
            </view>
          </view>

          <!-- 右上角状态印章 -->
          <view class="stamp-container">
            <StampCanvas :text="getStatusText(task.status)" />
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="task-actions">
          <button
            v-if="task.status === 'pending'"
            class="btn btn-shelving"
            @click.stop="startShelving(task)"
          >
            <uni-icons type="compose" size="20"></uni-icons>
            <text>上架</text>
          </button>
          <button
            v-if="task.status === 'temporary'"
            class="btn btn-temporary"
            @click.stop="completeTemporary(task)"
          >
            <uni-icons type="checkmarkempty" size="20"></uni-icons>
            <text>暂存</text>
          </button>
          <button class="btn btn-view" @click.stop="viewDetail(task)">
            <uni-icons type="eye" size="20"></uni-icons>
            <text>查看</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="taskList.length === 0" class="empty-state">
      <image src="/static/images/empty.png" class="empty-image"></image>
      <text class="empty-text">暂无待上架任务</text>
    </view>

    <!-- 加载更多 -->
    <view v-if="hasMore" class="load-more" @click="loadMore">
      <text>加载更多</text>
    </view>
  </view>
</template>

<script>
import StampCanvas from "../canvas/canvas.vue"

export default {
  components: {StampCanvas},
  data() {
    return {
      // 任务列表数据
      taskList: [
        {
          id: '1',
          batchNumber: '20152368652',
          purchaseOrderNo: 'CC202506235685',
          stockQuantity: 'xx',
          stockEntity: '合福酒业',
          shelvingQuantity: 2,
          status: 'shelve'
        },
        {
          id: '2',
          batchNumber: '20152368653', 
          purchaseOrderNo: 'CC202506235686',
          stockQuantity: '暂存区',
          stockEntity: '合福酒业',
          shelvingQuantity: 2,
          status: 'temporary'
        },
        {
          id: '3',
          batchNumber: '20152368654',
          purchaseOrderNo: 'CC202506235687', 
          stockQuantity: 'xx',
          stockEntity: '合福酒业',
          shelvingQuantity: 2,
          status: 'shelved'
        },
        {
          id: '4',
          batchNumber: '20152368655',
          purchaseOrderNo: '无',
          stockQuantity: 'XX',
          stockEntity: '合福酒业',
          shelvingQuantity: 2,
          status: 'stored'
        }
      ],
      hasMore: true,
      loading: false,
      currentStatus: '', // 当前选中的状态：'' 全部, 'pending' 待上架, 'temporary' 待暂存, 'stored' 已暂存, 'completed' 已上架
      currentPage: 1,
      pageSize: 10
    }
  },
  
  onLoad() {
    this.loadTaskList()
  },
  
  methods: {
    // 【后端接口】加载任务列表数据
    async loadTaskList() {
      try {
        this.loading = true
        // 【接口地址】GET /warehouse/shelving/task/list
        // 【请求参数】{ pageNum: 1, pageSize: 10, status: 'pending' }
        // 【返回数据】{
        //   code: 200,
        //   rows: [{
        //     id: string,
        //     batchNumber: string,      // 批次编号
        //     purchaseOrderNo: string,  // 采购单号
        //     stockQuantity: string,    // 货存数量
        //     stockEntity: string,      // 货存主体
        //     shelvingQuantity: number, // 上架数量
        //     status: string           // pending/in_progress/completed/temporary/stored
        //   }],
        //   total: 100
        // }
        // const response = await this.$api.getShelvingTaskList()
        // this.taskList = response.rows
        console.log('加载待上架任务列表')
      } catch (error) {
        console.error('加载任务列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 【后端接口】加载更多数据
    async loadMore() {
      if (this.loading) return
      try {
        this.loading = true
        // 【接口地址】GET /warehouse/shelving/task/list
        // 【请求参数】{ pageNum: this.currentPage + 1, pageSize: 10 }
        console.log('加载更多任务')
      } catch (error) {
        console.error('加载更多失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'shelve': '待上架',
        'shelved': '已上架', 
        'temporary': '待暂存',
        'stored': '已暂存'
      }
      return statusMap[status] || '待上架'
    },

    // 跳转到上架详情页
    goToShelvingDetail(task) {
      uni.navigateTo({
        url: `/pages/warehouse/shelving/shelving-detail?taskId=${task.id}`
      })
    },

    // 开始上架
    startShelving(task) {
      uni.navigateTo({
        url: `/pages/warehouse/shelving/shelving-detail?taskId=${task.id}&mode=shelving`
      })
    },

    // 【后端接口】完成暂存
    async completeTemporary(task) {
      try {
        // 【接口地址】POST /warehouse/shelving/task/{taskId}/temporary-complete
        // 【请求参数】taskId: string
        // 【返回数据】{ success: boolean, message: string }
        // await this.$api.completeTemporaryStorage(task.id)

        uni.showToast({
          title: '暂存完成',
          icon: 'success'
        })

        // 更新任务状态
        task.status = 'stored'

        console.log('完成暂存:', task.id)
      } catch (error) {
        console.error('完成暂存失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    },

    // 查看详情
    viewDetail(task) {
      uni.navigateTo({
        url: `/pages/warehouse/shelving/shelving-detail?taskId=${task.id}&mode=view`
      })
    },

    // 切换标签
    switchTab(status) {
      if (this.currentStatus === status) return

      this.currentStatus = status
      this.currentPage = 1
      this.taskList = []
      this.loadTaskList()
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部标签栏 */
.tab-bar {
  display: flex;
  background-color: #fff;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  font-size: 32rpx;
  color: #666;
  position: relative;

  &.active {
    color: #07c160;
    font-weight: 500;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 4rpx;
      background-color: #07c160;
      border-radius: 2rpx;
    }
  }
}

/* 任务列表 */
.task-list {
  padding: 16rpx 24rpx;
}

.task-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 任务头部 */
.task-header {
  margin-bottom: 12rpx;
}

.task-title {
  font-size: 22rpx;
  color: #666;
  font-weight: 400;
  margin-bottom: 2rpx;
}

.task-number {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}

/* 任务内容区域 */
.task-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
  position: relative;
}

.task-info {
  flex: 1;
}

.info-row {
  display: flex;
  margin-bottom: 8rpx;
  align-items: center;

  &:last-child {
    margin-bottom: 0;
  }

  &.full-width {
    .label {
      width: 120rpx;
    }
    .value {
      flex: 1;
    }
  }
}

.label {
  font-size: 26rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

.value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 分隔线 */
.divider-line {
  height: 1rpx;
  background-color: #e5e5e5;
  margin: 12rpx 0;
  width: calc(100% - 140px); /* 从左边到印章左边，考虑印章宽度125px + 一些间距 */
}

/* 印章容器 */
.stamp-container {
  position: absolute;
  top: -110rpx;
  right: -20rpx;
  z-index: 10;
}

/* 操作按钮 */
.task-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  margin-top: 16rpx;
  padding-right: 30rpx;
}

.task-item .task-actions button.btn {
  flex: none;
  width: auto;
  min-width: 120rpx;
  max-width: 150rpx;
  height: 56rpx;
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  font-size: 24rpx;
  border: 1rpx solid #e5e5e5;
  background-color: #fff;
  color: #333;
  padding: 0 20rpx;
  margin: 0;
  line-height: normal;
  box-sizing: border-box;
}

.task-item .task-actions button.btn.btn-shelving uni-icons {
  color: #ff6b35;
}

.task-item .task-actions button.btn.btn-temporary uni-icons {
  color: #ffa500;
}

.task-item .task-actions button.btn.btn-view uni-icons {
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 32rpx;
  color: #666;
  font-size: 28rpx;
}
</style>

