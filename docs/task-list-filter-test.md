# 任务列表标签栏筛选功能测试文档

## 功能概述

完善了任务列表页面顶部标签栏的筛选功能，用户可以通过点击不同的标签来筛选不同状态的任务。

## 实现功能

### 1. 标签栏筛选
- **全部**：显示所有任务
- **待收货**：只显示状态为 `pending` 的任务
- **已收货**：只显示状态为 `completed` 的任务

### 2. 任务数量显示
- 每个标签显示对应状态的任务数量
- 格式：`标签名(数量)`
- 实时更新数量统计

### 3. 筛选逻辑
- 使用本地筛选，无需重新请求接口
- 切换标签时立即显示筛选结果
- 显示切换成功的提示信息

### 4. 空状态处理
- 根据当前筛选状态显示不同的空状态文本
- 全部：暂无任务
- 待收货：暂无待收货任务
- 已收货：暂无已收货任务

## 测试数据

系统包含以下测试数据：

```javascript
// 待收货任务 (2个)
{
  id: '1',
  batchNumber: '20152368652',
  status: 'pending'
},
{
  id: '4', 
  batchNumber: '20152368655',
  status: 'pending'
}

// 已收货任务 (3个)
{
  id: '2',
  batchNumber: '20152368653', 
  status: 'completed'
},
{
  id: '3',
  batchNumber: '20152368654',
  status: 'completed'
},
{
  id: '5',
  batchNumber: '20152368656',
  status: 'completed'
}
```

## 测试步骤

### 1. 页面加载测试
1. 进入任务列表页面
2. 验证默认显示"全部"标签为激活状态
3. 验证标签数量显示：全部(5) 待收货(2) 已收货(3)
4. 验证显示所有5个任务

### 2. 筛选功能测试
1. **点击"待收货"标签**
   - 验证标签切换为激活状态
   - 验证只显示2个待收货任务
   - 验证显示"已切换到待收货"提示

2. **点击"已收货"标签**
   - 验证标签切换为激活状态
   - 验证只显示3个已收货任务
   - 验证显示"已切换到已收货"提示

3. **点击"全部"标签**
   - 验证标签切换为激活状态
   - 验证显示所有5个任务
   - 验证显示"已切换到全部"提示

### 3. 重复点击测试
1. 点击当前已激活的标签
2. 验证不会重复执行筛选
3. 验证不会显示重复的提示信息

### 4. 空状态测试
1. 如果某个状态下没有任务
2. 验证显示对应的空状态文本
3. 验证空状态图标正常显示

## 技术实现

### 数据结构
- `allTaskList`: 保存所有任务数据
- `taskList`: 当前显示的任务列表（经过筛选）
- `currentStatus`: 当前选中的状态

### 核心方法
- `applyFilter()`: 应用筛选逻辑
- `switchTab(status)`: 切换标签
- `statusCounts`: 计算各状态任务数量

### 筛选逻辑
```javascript
applyFilter() {
  if (!this.currentStatus) {
    this.taskList = [...this.allTaskList] // 全部
  } else {
    this.taskList = this.allTaskList.filter(task => task.status === this.currentStatus)
  }
}
```

## 样式特点

### 标签栏样式
- 使用flexbox布局，三个标签等宽分布
- 激活状态：绿色文字 + 底部绿色指示条
- 标签文字和数量垂直排列

### 响应式设计
- 标签文字：32rpx
- 数量文字：24rpx，灰色
- 激活状态下数量文字也变为绿色

## 注意事项

1. **性能优化**：使用本地筛选避免频繁请求接口
2. **用户体验**：切换标签时显示提示信息
3. **数据一致性**：确保数量统计与实际显示一致
4. **状态管理**：正确维护当前筛选状态

## 相关文件

- `pages/warehouse/receiving/task-list.vue` - 任务列表页面（已完善筛选功能）
