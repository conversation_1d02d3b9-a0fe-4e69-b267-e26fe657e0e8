<template>
  <view class="container">
    <!-- 货品详情 -->
    <view class="goods-detail">
      <view class="detail-header">
        <text class="title">货品详情</text>
        <uni-icons type="info" size="20" color="#666" @click="showGoodsInfo"></uni-icons>
      </view>

      <view class="detail-content">
        <view class="detail-row">
          <text class="label">货品编码</text>
          <!-- 根据是否属于当前批次决定显示方式 -->
          <input
            v-if="!belongsToCurrentBatch"
            v-model="goodsDetail.goodsCode"
            class="detail-input editable"
            type="text"
            placeholder="请输入货品编码"
          />
          <text v-else class="value">{{ goodsDetail.goodsCode }}</text>
        </view>
        <view class="detail-row">
          <text class="label">货品名称</text>
          <!-- 根据是否属于当前批次决定显示方式 -->
          <input
            v-if="!belongsToCurrentBatch"
            v-model="goodsDetail.goodsName"
            class="detail-input editable"
            type="text"
            placeholder="请输入货品名称"
          />
          <text v-else class="value">{{ goodsDetail.goodsName }}</text>
        </view>
        <view class="detail-row">
          <text class="label">规格</text>
          <!-- 根据是否属于当前批次决定显示方式 -->
          <input
            v-if="!belongsToCurrentBatch"
            v-model="goodsDetail.specification"
            class="detail-input editable"
            type="text"
            placeholder="请输入规格"
          />
          <text v-else class="value">{{ goodsDetail.specification }}</text>
        </view>
        <view class="detail-row">
          <text class="label">类型</text>
          <text class="value">{{ goodsDetail.type }}</text>
        </view>
        <view class="detail-row">
          <text class="label">包装数量</text>
          <view class="input-group">
            <input
              v-model="goodsDetail.packageQuantity"
              class="detail-input"
              type="number"
              placeholder="请输入包装数量"
            />
            <text class="unit">{{ goodsDetail.packageUnit }}</text>
          </view>
        </view>
        <view class="detail-row">
          <text class="label">尾数</text>
          <view class="input-group">
            <input
              v-model="goodsDetail.stockQuantity"
              class="detail-input"
              type="number"
              placeholder="请输入尾数"
            />
            <text class="unit">{{ goodsDetail.stockUnit }}</text>
          </view>
        </view>
        <view class="detail-row">
          <text class="label">生产日期</text>
          <view class="input-group date-input-group">
            <input
              v-model="goodsDetail.productionDate"
              class="detail-input"
              type="text"
              placeholder="请输入生产日期"
              @input="onProductionDateInput"
            />
            <text class="formatted-date">{{ formattedProductionDate }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 标签 -->
    <view class="tags-section">
      <view class="tags-header">
        <text class="title">标签</text>
        <view class="freeze-option" @click="toggleFreeze">
          <view class="custom-checkbox" :class="{ checked: isFrozen }">
            <view class="checkbox-inner" v-if="isFrozen">✓</view>
          </view>
          <text class="freeze-text">冻结</text>
        </view>
      </view>
      <view class="tags-content">
        <view
          v-for="(tag, index) in tags"
          :key="index"
          class="tag-item"
          :class="{ active: tag.active }"
          @click="toggleTag(index)"
        >
          {{ tag.name }}
        </view>
      </view>
    </view>

    <!-- 收货详情 -->
    <view class="receiving-detail">
      <view class="detail-header">
        <text class="title">收货详情</text>
      </view>

      <!-- 实收数量 -->
      <view class="quantity-section">
        <view class="quantity-row">
          <text class="quantity-label required">实收数量</text>
          <input
            v-model="actualQuantity"
            class="quantity-input"
            type="number"
            placeholder="输入实收数量"
          />
        </view>
      </view>

      <!-- 物流码 -->
      <view class="barcode-section">
        <view class="barcode-header" @click="toggleLogisticsCollapse">
          <uni-icons
            :type="logisticsCollapsed ? 'right' : 'bottom'"
            size="16"
            color="#666"
            class="collapse-icon"
          ></uni-icons>
          <uni-icons type="scan" size="16" color="#666"></uni-icons>
          <text class="barcode-label">物流码</text>
          <text class="barcode-note">输入或点击右侧扫描物流码</text>
          <button class="add-btn" @click.stop="addLogisticsCode">+</button>
          <button class="scan-btn" @click.stop="scanLogisticsCode">
            <uni-icons type="scan" size="16" color="#007aff"></uni-icons>
          </button>
        </view>
        <view class="barcode-list" v-show="!logisticsCollapsed">
          <view
            v-for="(code, index) in logisticsCodes"
            :key="index"
            class="barcode-item"
          >
            <input
              v-model="code.value"
              class="barcode-input"
              placeholder="请输入物流码"
              @input="onLogisticsCodeInput(index, $event)"
            />
            <button class="delete-btn" @click="deleteLogisticsCode(index)">
              <uni-icons type="close" size="16" color="#ff4d4f"></uni-icons>
            </button>
          </view>
        </view>
      </view>

      <!-- 唯一码 - 只有属于当前批次时才显示 -->
      <view v-if="belongsToCurrentBatch" class="barcode-section">
        <view class="barcode-header" @click="toggleUniqueCollapse">
          <uni-icons
            :type="uniqueCollapsed ? 'right' : 'bottom'"
            size="16"
            color="#666"
            class="collapse-icon"
          ></uni-icons>
          <uni-icons type="scan" size="16" color="#666"></uni-icons>
          <text class="barcode-label required">唯一码</text>
          <text class="barcode-note">点击右侧扫描按钮，扫描商品唯一码</text>
          <button class="scan-btn" @click.stop="scanUniqueCode">
            <uni-icons type="scan" size="16" color="#007aff"></uni-icons>
          </button>
        </view>
        <view class="barcode-list" v-show="!uniqueCollapsed">
          <view
            v-for="(code, index) in uniqueCodes"
            :key="index"
            class="barcode-item"
          >
            <input
              v-model="code.value"
              class="barcode-input"
              placeholder="请输入唯一码"
              @input="onUniqueCodeInput(index, $event)"
            />
            <button class="delete-btn" @click="deleteUniqueCode(index)">
              <uni-icons type="close" size="16" color="#ff4d4f"></uni-icons>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <!-- 只有不属于当前批次时才显示继续收货按钮 -->
      <button
        v-if="!belongsToCurrentBatch"
        class="action-btn btn-success"
        @click="continueToReceiveGoods"
      >
        继续收货
      </button>
      <button class="action-btn btn-primary" @click="completeTask">完成</button>
      <button class="action-btn btn-secondary" @click="cancelTask">取消</button>
    </view>

    <!-- 暂存任务特殊界面 -->
    <view v-if="isTemporaryTask" class="temporary-overlay">
      <view class="temporary-content">
        <view class="temporary-header">
          <uni-icons type="info-filled" size="40" color="#fa8c16"></uni-icons>
          <text class="temporary-title">暂存任务</text>
        </view>
        <view class="temporary-description">
          <text>此任务为暂存任务，等待数据中台编码维护完成后自动转为待上架状态。</text>
        </view>
        <view class="temporary-actions">
          <button class="temporary-btn" @click="completeTemporary">完成暂存</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      taskId: '',
      mode: 'receiving', // receiving: 收货模式, view: 查看模式
      isTemporaryTask: false, // 是否为暂存任务
      goodsCode: '', // 从扫码页面传来的货品编码
      fromSource: '', // 来源标识
      belongsToCurrentBatch: true, // 是否属于当前批次，默认为true

      // 货品详情
      goodsDetail: {
        goodsCode: '010101010001',
        goodsName: '53度郎酒红花郎',
        specification: '1650ml',
        type: '商品',
        typeStatus: '是',
        packageQuantity: '6',
        packageUnit: '箱',
        stockQuantity: '0',
        stockUnit: '瓶',
        productionDate: '20250717',
        productionDateFormatted: '2025-07-17'
      },

      // 标签
      tags: [
        { name: '定向销售', active: true },
        { name: '串货', active: false },
        { name: '外采', active: false },
        { name: '储值', active: false },
        { name: '自定义标签', active: false }
      ],

      // 冻结状态
      isFrozen: false,

      // 实收数量
      actualQuantity: 2,

      // 折叠状态
      logisticsCollapsed: false,
      uniqueCollapsed: false,

      // 物流码列表
      logisticsCodes: [
        { value: '1023222225232' },
        { value: '1023222225232' }
      ],

      // 唯一码列表
      uniqueCodes: [
        { value: '8565235822256' },
        { value: '8563256472145' }
      ]
    }
  },

  computed: {
    // 格式化生产日期显示
    formattedProductionDate() {
      const date = this.goodsDetail.productionDate
      if (!date || date.length !== 8) {
        return ''
      }

      // 将 YYYYMMDD 格式转换为 YYYY-MM-DD
      const year = date.substring(0, 4)
      const month = date.substring(4, 6)
      const day = date.substring(6, 8)

      // 验证日期是否有效
      const dateObj = new Date(year, month - 1, day)
      if (dateObj.getFullYear() != year ||
          dateObj.getMonth() != month - 1 ||
          dateObj.getDate() != day) {
        return ''
      }

      return `${year}-${month}-${day}`
    }
  },

  onLoad(options) {
    this.taskId = options.taskId || ''
    this.mode = options.mode || 'shelving'
    this.goodsCode = options.goodsCode || '' // 从扫码页面传来的货品编码
    this.fromSource = options.from || '' // 来源标识

    // 获取批次状态信息，默认为属于当前批次
    this.belongsToCurrentBatch = options.belongsToCurrentBatch !== 'false'

    console.log('上架详情页面参数:', {
      taskId: this.taskId,
      mode: this.mode,
      goodsCode: this.goodsCode,
      from: this.fromSource,
      belongsToCurrentBatch: this.belongsToCurrentBatch
    })

    this.loadGoodsDetail()
  },
  
  methods: {
    // 【后端接口】加载货品详情
    async loadGoodsDetail() {
      try {
        // 【接口地址】GET /warehouse/shelving/goods/{taskId}
        // 【请求参数】taskId: string
        // 【返回数据】{
        //   goodsCode: string,              // 货品编码
        //   goodsName: string,              // 货品名称
        //   specification: string,          // 规格
        //   shouldShelvingQuantity: number, // 应上架数量
        //   unit: string,                   // 单位
        //   type: string,                   // 类型
        //   recommendedLocation: string,    // 推荐库位
        //   isTemporary: boolean            // 是否暂存任务
        // }
        // const response = await this.$api.getShelvingGoodsDetail(this.taskId)
        // this.goodsDetail = response.data
        // this.isTemporaryTask = response.data.isTemporary

        console.log('加载上架货品详情:', this.taskId)

        // 模拟暂存任务
        if (this.taskId === '4') {
          this.isTemporaryTask = true
        }
      } catch (error) {
        console.error('加载货品详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      }
    },

    // 显示货品信息弹窗
    showGoodsInfo() {
      uni.showModal({
        title: '货品信息',
        content: `编码: ${this.goodsDetail.goodsCode}\n名称: ${this.goodsDetail.goodsName}\n推荐库位: ${this.goodsDetail.recommendedLocation}`,
        showCancel: false
      })
    },

    // 生产日期输入处理
    onProductionDateInput(event) {
      let value = event.detail.value
      // 只允许输入数字
      value = value.replace(/\D/g, '')
      // 限制最大长度为8位
      if (value.length > 8) {
        value = value.substring(0, 8)
      }
      this.goodsDetail.productionDate = value
      console.log('生产日期输入:', value, '格式化:', this.formattedProductionDate)
    },



    // 标签操作
    toggleTag(index) {
      this.tags[index].active = !this.tags[index].active
      console.log('切换标签:', this.tags[index])
    },

    toggleFreeze() {
      this.isFrozen = !this.isFrozen
      console.log('切换冻结状态:', this.isFrozen)
    },

    // 折叠切换
    toggleLogisticsCollapse() {
      this.logisticsCollapsed = !this.logisticsCollapsed
      console.log('切换物流码折叠状态:', this.logisticsCollapsed)
    },

    toggleUniqueCollapse() {
      this.uniqueCollapsed = !this.uniqueCollapsed
      console.log('切换唯一码折叠状态:', this.uniqueCollapsed)
    },

    // 物流码操作
    addLogisticsCode() {
      this.logisticsCodes.push({ value: '' })
      console.log('添加物流码输入框')
    },

    deleteLogisticsCode(index) {
      this.logisticsCodes.splice(index, 1)
      console.log('删除物流码:', index)
    },

    onLogisticsCodeInput(index, event) {
      this.logisticsCodes[index].value = event.detail.value
      console.log('物流码输入:', index, event.detail.value)
    },

    scanLogisticsCode() {
      uni.scanCode({
        success: (res) => {
          console.log('扫描物流码结果:', res.result)
          // 添加到物流码列表
          this.logisticsCodes.push({ value: res.result })
          uni.showToast({
            title: '扫码成功',
            icon: 'success'
          })
        },
        fail: (error) => {
          console.error('扫码失败:', error)
          uni.showToast({
            title: '扫码失败',
            icon: 'error'
          })
        }
      })
    },

    // 唯一码操作
    deleteUniqueCode(index) {
      this.uniqueCodes.splice(index, 1)
      console.log('删除唯一码:', index)
    },

    onUniqueCodeInput(index, event) {
      this.uniqueCodes[index].value = event.detail.value
      console.log('唯一码输入:', index, event.detail.value)
    },

    scanUniqueCode() {
      uni.scanCode({
        success: (res) => {
          console.log('扫描唯一码结果:', res.result)
          // 添加到唯一码列表
          this.uniqueCodes.push({ value: res.result })
          this.actualQuantity = this.uniqueCodes.length
          uni.showToast({
            title: '扫码成功',
            icon: 'success'
          })
        },
        fail: (error) => {
          console.error('扫码失败:', error)
          uni.showToast({
            title: '扫码失败',
            icon: 'error'
          })
        }
      })
    },

    // 继续收货
    continueToReceiveGoods() {
      // 跳转到扫码页面
      uni.navigateTo({
        url: `/pages/warehouse/receiving/scan?taskId=${this.taskId}`
      })
    },

    // 【后端接口】确认收货
    async confirmReceiving() {
      try {
        // 验证数据
        if (this.actualQuantity <= 0) {
          uni.showToast({
            title: '请输入实收数量',
            icon: 'error'
          })
          return
        }

        if (this.uniqueCodes.length === 0) {
          uni.showToast({
            title: '请扫描唯一码',
            icon: 'error'
          })
          return
        }

        // 【接口地址】POST /warehouse/receiving/confirm
        // 【请求参数】{
        //   taskId: string,
        //   goodsCode: string,
        //   actualQuantity: number,
        //   logisticsCodes: string[],    // 物流码列表
        //   uniqueCodes: string[],       // 唯一码列表
        //   tags: string[],              // 选中的标签
        //   operatorId: string,
        //   operateTime: string
        // }
        // 【返回数据】{ success: boolean, message: string }
        // const response = await this.$api.confirmReceiving({
        //   taskId: this.taskId,
        //   goodsCode: this.goodsDetail.goodsCode,
        //   actualQuantity: this.actualQuantity,
        //   logisticsCodes: this.logisticsCodes.map(item => item.value).filter(v => v),
        //   uniqueCodes: this.uniqueCodes.map(item => item.value).filter(v => v),
        //   tags: this.tags.filter(tag => tag.active).map(tag => tag.name)
        // })

        uni.showToast({
          title: '收货成功',
          icon: 'success'
        })

        console.log('确认收货成功')
      } catch (error) {
        console.error('确认收货失败:', error)
        uni.showToast({
          title: '收货失败',
          icon: 'error'
        })
      }
    },

    // 【后端接口】完成任务
    async completeTask() {
      try {
        // 【接口地址】POST /warehouse/shelving/task/{taskId}/complete
        // 【请求参数】taskId: string
        // 【返回数据】{ success: boolean, message: string }
        // await this.$api.completeShelvingTask(this.taskId)

        uni.showToast({
          title: '任务完成',
          icon: 'success'
        })

        // 跳转到上架任务列表页面
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/warehouse/shelving/task-list'
          })
        }, 1500)

        console.log('完成上架任务')
      } catch (error) {
        console.error('完成任务失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    },

    // 取消任务
    cancelTask() {
      uni.navigateBack()
    },

    // 【后端接口】完成暂存
    async completeTemporary() {
      try {
        // 【接口地址】POST /warehouse/shelving/task/{taskId}/temporary-complete
        // 【请求参数】taskId: string
        // 【返回数据】{ success: boolean, message: string }
        // await this.$api.completeTemporaryStorage(this.taskId)

        uni.showToast({
          title: '暂存完成',
          icon: 'success'
        })

        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

        console.log('完成暂存任务')
      } catch (error) {
        console.error('完成暂存失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
  position: relative;
}

.goods-detail, .tags-section, .receiving-detail {
  background-color: #fff;
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.detail-content {
  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: 28rpx;
      color: #666;
      width: 160rpx;
    }

    .value {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      flex: 1;
      text-align: right;

      &.date-highlight {
        color: #007aff;
        font-weight: 600;
      }
    }

    .type-status {
      font-size: 28rpx;
      color: #52c41a;
      font-weight: 600;
      margin-left: 16rpx;
    }

    .unit {
      font-size: 28rpx;
      color: #333;
      margin-left: 8rpx;
    }

    .input-group {
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: flex-end;

      .detail-input {
        width: 200rpx;
        height: 60rpx;
        padding: 0 16rpx;
        border: 2rpx solid #d9d9d9;
        border-radius: 6rpx;
        font-size: 28rpx;
        color: #333;
        text-align: right;

        &:focus {
          border-color: #007aff;
        }
      }
    }
  }
}

// 可编辑输入框的独立样式
.detail-input.editable {
  flex: 1;
  width: auto;
  height: 60rpx;
  padding: 0 16rpx;
  border: 2rpx solid #c7c7c7;
  border-radius: 6rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #FFFFFF;
  text-align: right;

  &:focus {
    background-color: #fff;
    border-color: #c7c7c7;
    box-shadow: 0 0 0 2rpx rgba(24, 144, 255, 0.2);
  }
}

.goods-detail {
  .detail-content {
    .detail-row {
      .input-group {
        display: flex;
        align-items: center;
        flex: 1;
        justify-content: flex-end;

        .detail-input {
          width: 200rpx;
          height: 60rpx;
          padding: 0 16rpx;
          border: 2rpx solid #d9d9d9;
          border-radius: 6rpx;
          font-size: 28rpx;
          color: #333;
          text-align: right;

          &:focus {
            border-color: #007aff;
          }
        }

        &.date-input-group {
          gap: 16rpx;

          .formatted-date {
            font-size: 28rpx;
            color: #007aff;
            font-weight: 600;
            min-width: 200rpx;
            text-align: right;
          }
        }
      }
    }
  }
}

// 标签样式
.tags-section {
  .tags-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;

    .title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }

    .freeze-option {
      display: flex;
      align-items: center;
      gap: 8rpx;
      cursor: pointer;

      .custom-checkbox {
        width: 32rpx;
        height: 32rpx;
        border: 2rpx solid #d9d9d9;
        border-radius: 4rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        transition: all 0.2s;

        &.checked {
          border-color: #007aff;
          background-color: #007aff;

          .checkbox-inner {
            color: #fff;
            font-size: 20rpx;
            font-weight: bold;
            line-height: 1;
          }
        }
      }

      .freeze-text {
        font-size: 26rpx;
        color: #333;
      }
    }
  }

  .tags-content {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .tag-item {
      padding: 12rpx 24rpx;
      border: 2rpx solid #d9d9d9;
      border-radius: 32rpx;
      font-size: 24rpx;
      color: #666;
      background-color: #fff;
      cursor: pointer;

      &.active {
        border-color: #007aff;
        color: #007aff;
        background-color: #f0f8ff;
      }
    }
  }
}

// 收货详情样式
.receiving-detail {
  .quantity-section {
    margin-bottom: 32rpx;

    .quantity-row {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .quantity-label {
        font-size: 28rpx;
        color: #333;
        width: 160rpx;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4rpx;
        }
      }

      .quantity-input {
        flex: 1;
        height: 60rpx;
        padding: 0 16rpx;
        border: 2rpx solid #d9d9d9;
        border-radius: 6rpx;
        font-size: 28rpx;
        color: #333;

        &:focus {
          border-color: #007aff;
        }
      }
    }
  }

  .barcode-section {
    margin-bottom: 32rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .barcode-header {
      display: flex;
      align-items: center;
      gap: 8rpx;
      margin-bottom: 16rpx;
      cursor: pointer;
      padding: 8rpx;
      border-radius: 8rpx;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f5f5f5;
      }

      .barcode-label {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;

        &.required::before {
          content: '*';
          color: #ff4d4f;
          margin-right: 4rpx;
        }
      }

      .barcode-note {
        flex: 1;
        font-size: 24rpx;
        color: #999;
      }

      .add-btn {
        width: 60rpx;
        height: 60rpx;
        background-color: #007aff;
        color: #fff;
        border: none;
        border-radius: 8rpx;
        font-size: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8rpx;
      }

      .scan-btn {
        width: 60rpx;
        height: 60rpx;
        background-color: #f0f0f0;
        border: none;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8rpx;
      }

      .collapse-icon {
        margin-right: 8rpx;
        transition: transform 0.2s;
      }
    }

    .barcode-list {
      overflow: hidden;
      transition: max-height 0.3s ease-in-out;

      .barcode-item {
        display: flex;
        align-items: center;
        gap: 16rpx;
        margin-bottom: 16rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .barcode-input {
          flex: 1;
          height: 80rpx;
          padding: 0 24rpx;
          border: 2rpx solid #d9d9d9;
          border-radius: 8rpx;
          font-size: 28rpx;
          color: #333;

          &:focus {
            border-color: #007aff;
          }
        }

        .delete-btn {
          width: 60rpx;
          height: 60rpx;
          background-color: #fff;
          border: 2rpx solid #ff4d4f;
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 16rpx 24rpx;
  display: flex;
  gap: 12rpx;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);

  .action-btn {
    flex: 1;
    height: 72rpx;
    border: none;
    border-radius: 6rpx;
    font-size: 28rpx;
    font-weight: 500;

    &.btn-success {
      background-color: #52c41a;
      color: #fff;
    }

    &.btn-primary {
      background-color: #007aff;
      color: #fff;
    }

    &.btn-secondary {
      background-color: #f0f0f0;
      color: #333;
    }
  }
}

// 暂存任务特殊样式
.temporary-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .temporary-content {
    background-color: #fff;
    margin: 0 64rpx;
    border-radius: 16rpx;
    padding: 48rpx 32rpx;
    text-align: center;
    
    .temporary-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 32rpx;
      
      .temporary-title {
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
        margin-top: 16rpx;
      }
    }
    
    .temporary-description {
      margin-bottom: 48rpx;
      
      text {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
      }
    }
    
    .temporary-actions {
      .temporary-btn {
        width: 200rpx;
        height: 80rpx;
        background-color: #fa8c16;
        color: #fff;
        border: none;
        border-radius: 8rpx;
        font-size: 32rpx;
        font-weight: 500;
      }
    }
  }
}
</style>
