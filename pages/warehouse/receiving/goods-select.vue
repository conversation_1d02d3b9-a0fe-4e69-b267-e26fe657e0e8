<template>
  <view class="goods-select-container">
    <!-- 顶部搜索栏 -->
    <view class="search-bar">
      <view class="search-input-wrapper">
        <uni-icons type="search" size="18" color="#999"></uni-icons>
        <input 
          class="search-input" 
          placeholder="搜索货品名称或编码"
          v-model="searchKeyword"
          @input="onSearchInput"
          @confirm="searchGoods"
        />
        <view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
          <uni-icons type="clear" size="16" color="#999"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 批次信息 -->
    <view class="batch-info">
      <text class="batch-label">当前批次：</text>
      <text class="batch-number">{{ batchNumber }}</text>
    </view>

    <!-- 货品列表 -->
    <view class="goods-list">
      <view 
        v-for="(goods, index) in filteredGoodsList" 
        :key="goods.goodsCode"
        class="goods-item"
        @click="selectGoods(goods)"
      >
        <!-- 货品基本信息 -->
        <view class="goods-info">
          <view class="goods-details">
            <view class="detail-row">
              <text class="label">物料编码：</text>
              <text class="value">{{ goods.goodsCode }}</text>
            </view>
            <view class="detail-row">
              <text class="label">商品名：</text>
              <text class="value">{{ goods.goodsName }}</text>
            </view>
          </view>
        </view>

        <!-- 右侧确认按钮 -->
        <view class="confirm-btn" @click.stop="confirmSelect(goods)">
          <text class="confirm-text">确认</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="filteredGoodsList.length === 0 && !loading" class="empty-state">
      <view class="empty-icon">
        <uni-icons type="info" size="80" color="#ccc"></uni-icons>
      </view>
      <text class="empty-text">{{ searchKeyword ? '未找到相关货品' : '暂无货品信息' }}</text>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
    </view>
  </view>
</template>

<script>
import { getBatchGoodsList } from '@/api/warehouse'

export default {
  data() {
    return {
      // 页面参数
      taskId: '',
      batchNumber: '',
      
      // 搜索相关
      searchKeyword: '',
      searchTimer: null,
      
      // 货品列表数据
      goodsList: [],
      filteredGoodsList: [],
      loading: false,

      // 加载组件文本配置
      loadingText: {
        contentdown: '加载中...',
        contentrefresh: '加载中...',
        contentnomore: '加载完成'
      }
    }
  },
  
  onLoad(options) {
    // 获取页面参数
    this.taskId = options.taskId || ''
    this.batchNumber = options.batchNumber || ''
    
    console.log('手动选择物料页面参数:', { taskId: this.taskId, batchNumber: this.batchNumber })
    
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: '选择物料'
    })
    
    // 加载货品列表
    this.loadGoodsList()
  },
  
  methods: {
    // 【后端接口】加载批次货品列表
    async loadGoodsList() {
      try {
        this.loading = true
        
        // 【真实接口调用】
        const response = await getBatchGoodsList({
          taskId: this.taskId,
          batchNumber: this.batchNumber,
          keyword: this.searchKeyword
        })
        
        if (response.code === 200) {
          this.goodsList = response.data || []
          this.filterGoodsList()
        } else {
          throw new Error(response.msg || '获取货品列表失败')
        }
        
        console.log('加载批次货品列表成功:', this.goodsList.length)
        
      } catch (error) {
        console.error('加载货品列表失败:', error)
        
        // 开发环境使用模拟数据
        if (process.env.NODE_ENV === 'development') {
          console.log('使用模拟货品数据')
          this.goodsList = [
            {
              goodsCode: '6856236585695',
              goodsName: '五粮液52度500ml'
            },
            {
              goodsCode: '6856236585696',
              goodsName: '五粮液39度500ml'
            },
            {
              goodsCode: '6856236585697',
              goodsName: '五粮液礼盒装'
            },
            {
              goodsCode: '6856236585698',
              goodsName: '五粮液特曲'
            },
            {
              goodsCode: '6856236585699',
              goodsName: '剑南春52度'
            },
            {
              goodsCode: '6856236585700',
              goodsName: '茅台酒53度'
            }
          ]
          this.filterGoodsList()
        } else {
          uni.showToast({
            title: '加载失败',
            icon: 'error'
          })
        }
      } finally {
        this.loading = false
      }
    },
    
    // 过滤货品列表
    filterGoodsList() {
      if (!this.searchKeyword.trim()) {
        this.filteredGoodsList = [...this.goodsList]
      } else {
        const keyword = this.searchKeyword.toLowerCase()
        this.filteredGoodsList = this.goodsList.filter(goods =>
          goods.goodsName.toLowerCase().includes(keyword) ||
          goods.goodsCode.toLowerCase().includes(keyword)
        )
      }
    },
    
    // 搜索输入处理
    onSearchInput() {
      // 防抖处理
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      
      this.searchTimer = setTimeout(() => {
        this.filterGoodsList()
      }, 300)
    },
    
    // 执行搜索
    searchGoods() {
      this.filterGoodsList()
    },
    
    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.filterGoodsList()
    },
    

    
    // 选择货品（点击整行）
    selectGoods(goods) {
      // 点击整行时不执行任何操作，只有点击确认按钮才选择
    },

    // 确认选择货品
    confirmSelect(goods) {
      console.log('确认选择货品:', goods)

      // 跳转到上架详情页面
      uni.navigateTo({
        url: `/pages/warehouse/shelving/shelving-detail?taskId=${this.taskId}&goodsCode=${goods.goodsCode}&mode=shelving&from=manual`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.goods-select-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  background-color: #fff;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 24rpx;
  padding: 16rpx 24rpx;
  gap: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  
  &::placeholder {
    color: #999;
  }
}

.clear-btn {
  padding: 8rpx;
}

/* 批次信息 */
.batch-info {
  background-color: #fff;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.batch-label {
  font-size: 28rpx;
  color: #666;
}

.batch-number {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

/* 货品列表 */
.goods-list {
  padding: 0 24rpx;
}

.goods-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.goods-info {
  flex: 1;
}

.goods-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-row {
  display: flex;
  align-items: center;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.confirm-btn {
  background-color: #007AFF;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  margin-left: 24rpx;
}

.confirm-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  padding: 60rpx 0;
}
</style>
