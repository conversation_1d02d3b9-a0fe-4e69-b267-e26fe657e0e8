<template>
  <view class="scan-container">
    <!-- 顶部标题栏 -->
    <view class="header">
      <view class="back-btn" @click="goBack">
        <uni-icons type="left" size="20" color="#333"></uni-icons>
      </view>
      <view class="title">扫一扫</view>
      <view class="placeholder"></view>
    </view>

    <!-- 扫码提示文字 -->
    <view class="scan-tip">
      <text>扫描库位码</text>
    </view>

    <!-- 扫码区域 -->
    <view class="scan-area">
      <!-- 扫码框 -->
      <view class="scan-frame">
        <!-- 四个角的装饰 -->
        <view class="corner corner-top-left"></view>
        <view class="corner corner-top-right"></view>
        <view class="corner corner-bottom-left"></view>
        <view class="corner corner-bottom-right"></view>
        
        <!-- 扫描线动画 -->
        <view class="scan-line" :class="{ scanning: isScanning }"></view>
        
        <!-- 扫码结果显示 -->
        <view v-if="scannedLocationCode" class="scan-result-display">
          <view class="barcode-container">
            <!-- 条码图片区域 -->
            <view class="barcode-image">
              <view class="barcode-lines">
                <view v-for="i in 20" :key="i" class="barcode-line" :style="{ width: Math.random() * 4 + 1 + 'px' }"></view>
              </view>
            </view>
            <!-- 库位码文字 -->
            <text class="location-code">{{ scannedLocationCode }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 当前库位显示 -->
    <view v-if="scannedLocationCode" class="current-location">
      <text class="location-label">当前库位：</text>
      <text class="location-value">{{ currentLocationName }}</text>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-buttons">
      <!-- 开始扫码按钮 -->
      <button class="scan-btn" @click="startScan" :disabled="isScanning">
        <uni-icons type="camera" size="20" color="#fff"></uni-icons>
        <text>{{ isScanning ? '扫码中...' : '开始扫码' }}</text>
      </button>

      <!-- 开发环境测试按钮 -->
      <button v-if="isDev" class="test-btn" @click="testScan">
        <uni-icons type="compose" size="20" color="#FF9500"></uni-icons>
        <text>测试扫码</text>
      </button>

      <!-- 手动输入库位码按钮 -->
      <button class="manual-btn" @click="manualInput">
        <uni-icons type="compose" size="20" color="#007AFF"></uni-icons>
        <text>扫描商品条码</text>
      </button>
    </view>

    <!-- 扫码结果提示 -->
    <view v-if="scanResult" class="scan-result">
      <view class="result-item" :class="scanResult.type">
        <view class="result-icon">
          <uni-icons :type="scanResult.type === 'success' ? 'checkmarkempty' : 'info'" 
                     :color="scanResult.type === 'success' ? '#4CD964' : '#FF9500'" 
                     size="16"></uni-icons>
        </view>
        <text class="result-text">{{ scanResult.message }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 页面参数
      taskId: '',
      
      // 扫码状态
      isScanning: false,
      scannedLocationCode: '',
      currentLocationName: '',
      scanResult: null,
      
      // 开发环境标识
      isDev: process.env.NODE_ENV === 'development'
    }
  },
  
  onLoad(options) {
    // 获取页面参数
    this.taskId = options.taskId || ''
    console.log('库位扫码页面参数:', { taskId: this.taskId })
  },

  onShow() {
    console.log('库位扫码页面显示')
  },
  
  onHide() {
    // 页面隐藏时停止扫码
    this.stopScan()
  },
  
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 开始扫码
    startScan() {
      console.log('开始扫描库位码...')
      this.isScanning = true

      // 调用uni-app扫码API
      uni.scanCode({
        success: (res) => {
          console.log('扫码成功:', res)
          this.handleScanResult(res.result)
        },
        fail: (error) => {
          console.error('扫码失败:', error)
          this.isScanning = false

          if (error.errMsg && error.errMsg.includes('cancel')) {
            // 用户取消扫码
            console.log('用户取消扫码')
            uni.showToast({
              title: '已取消扫码',
              icon: 'none'
            })
          } else {
            console.error('扫码错误详情:', error)
            uni.showToast({
              title: '扫码失败，请重试',
              icon: 'error'
            })
          }
        },
        complete: () => {
          console.log('扫码操作完成')
        }
      })
    },
    
    // 停止扫码
    stopScan() {
      this.isScanning = false
    },

    // 测试扫码（开发环境）
    testScan() {
      console.log('测试库位码扫码功能')

      // 显示测试码选择弹窗
      uni.showActionSheet({
        itemList: [
          'A-01 (集采中心仓 - A01)',
          'B-02 (集采中心仓 - B02)', 
          'C-03 (集采中心仓 - C03)',
          'D-04 (集采中心仓 - D04)'
        ],
        success: (res) => {
          const testCodes = ['A-01', 'B-02', 'C-03', 'D-04']
          const selectedCode = testCodes[res.tapIndex]

          uni.showToast({
            title: `模拟扫码: ${selectedCode}`,
            icon: 'none',
            duration: 1500
          })

          setTimeout(() => {
            console.log('开始处理测试扫码结果:', selectedCode)
            this.handleScanResult(selectedCode)
          }, 1500)
        },
        fail: () => {
          console.log('用户取消选择测试码')
        }
      })
    },

    // 处理扫码结果
    async handleScanResult(code) {
      this.isScanning = false
      console.log('处理库位码扫码结果:', code)

      try {
        // 验证库位码
        const isValidLocation = await this.validateLocationCode(code)
        
        if (isValidLocation) {
          this.scannedLocationCode = code
          this.currentLocationName = `集采中心仓 - ${code}`
          
          this.showScanSuccess('库位码扫描成功')
          
          // 延迟跳转到上架详情页面
          setTimeout(() => {
            uni.navigateTo({
              url: `/pages/warehouse/shelving/shelving-detail?taskId=${this.taskId}&locationCode=${code}&mode=shelving&belongsToCurrentBatch=true`
            })
          }, 2000)
        } else {
          this.showScanError('无效的库位码，请重新扫描')
        }
      } catch (error) {
        console.error('处理库位码失败:', error)
        this.showScanError('处理库位码失败，请重试')
      }
    },

    // 验证库位码
    async validateLocationCode(code) {
      try {
        // 这里应该调用后端接口验证库位码
        // 【接口地址】POST /warehouse/location/validate
        // 【请求参数】{ locationCode: string }
        // 【返回数据】{ valid: boolean, locationName: string }
        
        // 模拟验证逻辑
        const validCodes = ['A-01', 'B-02', 'C-03', 'D-04']
        return validCodes.includes(code)
      } catch (error) {
        console.error('验证库位码失败:', error)
        return false
      }
    },

    // 显示扫码成功
    showScanSuccess(message) {
      this.scanResult = {
        type: 'success',
        message: message
      }
      
      setTimeout(() => {
        this.scanResult = null
      }, 3000)
    },

    // 显示扫码错误
    showScanError(message) {
      this.scanResult = {
        type: 'error',
        message: message
      }
      
      setTimeout(() => {
        this.scanResult = null
      }, 3000)
    },

    // 手动输入（跳转到商品扫码）
    manualInput() {
      if (!this.scannedLocationCode) {
        uni.showToast({
          title: '请先扫描库位码',
          icon: 'error'
        })
        return
      }
      
      // 跳转到商品扫码页面
      uni.navigateTo({
        url: `/pages/warehouse/receiving/scan?taskId=${this.taskId}&locationCode=${this.scannedLocationCode}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.scan-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
}

/* 顶部标题栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  padding-top: calc(var(--status-bar-height) + 20rpx);
  
  .back-btn, .placeholder {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .back-btn {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    
    &:active {
      background-color: rgba(255, 255, 255, 0.3);
    }
  }
  
  .title {
    color: #fff;
    font-size: 36rpx;
    font-weight: 600;
  }
}

/* 扫码提示 */
.scan-tip {
  text-align: center;
  margin: 60rpx 0 80rpx;
  
  text {
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
  }
}

/* 扫码区域 */
.scan-area {
  display: flex;
  justify-content: center;
  margin-bottom: 60rpx;
}

.scan-frame {
  width: 500rpx;
  height: 500rpx;
  position: relative;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  
  /* 四个角的装饰 */
  .corner {
    position: absolute;
    width: 60rpx;
    height: 60rpx;
    border: 6rpx solid #fff;
    
    &.corner-top-left {
      top: 20rpx;
      left: 20rpx;
      border-right: none;
      border-bottom: none;
      border-top-left-radius: 12rpx;
    }
    
    &.corner-top-right {
      top: 20rpx;
      right: 20rpx;
      border-left: none;
      border-bottom: none;
      border-top-right-radius: 12rpx;
    }
    
    &.corner-bottom-left {
      bottom: 20rpx;
      left: 20rpx;
      border-right: none;
      border-top: none;
      border-bottom-left-radius: 12rpx;
    }
    
    &.corner-bottom-right {
      bottom: 20rpx;
      right: 20rpx;
      border-left: none;
      border-top: none;
      border-bottom-right-radius: 12rpx;
    }
  }
  
  /* 扫描线动画 */
  .scan-line {
    position: absolute;
    left: 40rpx;
    right: 40rpx;
    height: 4rpx;
    background: linear-gradient(90deg, transparent, #00ff00, transparent);
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    
    &.scanning {
      animation: scanAnimation 2s linear infinite;
      opacity: 1;
    }
  }
  
  /* 扫码结果显示 */
  .scan-result-display {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 12rpx;
    padding: 40rpx;
    text-align: center;
    
    .barcode-container {
      .barcode-image {
        width: 200rpx;
        height: 120rpx;
        background-color: #fff;
        border: 2rpx solid #ddd;
        border-radius: 8rpx;
        margin: 0 auto 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .barcode-lines {
          display: flex;
          align-items: center;
          gap: 2rpx;
          
          .barcode-line {
            background-color: #000;
            height: 80rpx;
          }
        }
      }
      
      .location-code {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }
    }
  }
}

/* 当前库位显示 */
.current-location {
  text-align: center;
  margin-bottom: 60rpx;
  
  .location-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 28rpx;
  }
  
  .location-value {
    color: #00ff00;
    font-size: 32rpx;
    font-weight: 600;
    margin-left: 10rpx;
  }
}

/* 操作按钮区域 */
.action-buttons {
  padding: 0 60rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.scan-btn, .test-btn, .manual-btn {
  height: 88rpx;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  
  &:disabled {
    opacity: 0.6;
  }
}

.scan-btn {
  background-color: #007aff;
  color: #fff;
  
  &:active:not(:disabled) {
    background-color: #0056cc;
  }
}

.test-btn {
  background-color: #ff9500;
  color: #fff;
  
  &:active {
    background-color: #cc7700;
  }
}

.manual-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  
  &:active {
    background-color: rgba(255, 255, 255, 0.3);
  }
}

/* 扫码结果提示 */
.scan-result {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.result-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 32rpx 48rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  
  &.success {
    border-left: 8rpx solid #4cd964;
  }
  
  &.error {
    border-left: 8rpx solid #ff9500;
  }
  
  .result-text {
    font-size: 28rpx;
    color: #333;
  }
}

/* 扫描动画 */
@keyframes scanAnimation {
  0% {
    top: 40rpx;
  }
  50% {
    top: 50%;
  }
  100% {
    top: calc(100% - 40rpx);
  }
}
</style>
