# 扫码一对多货品选择功能测试文档

## 功能概述

当货品条码与商品编码存在一对多关系时，扫码后需要显示一个选择页面让用户手动确认作业货品。该页面与手动选择物料页面相同，但不包含上方的搜索框及物料批次信息。

## 实现方案

### 1. 修改扫码逻辑
- 在 `pages/warehouse/receiving/scan.vue` 中修改 `handleScanResult` 方法
- 当API返回 `multipleGoods` 字段且包含多个商品时，跳转到货品确认页面
- 添加测试用例支持一对多关系的模拟数据

### 2. 新增货品确认页面
- 创建 `pages/warehouse/receiving/goods-confirm.vue` 页面
- 页面结构类似于 `goods-select.vue`，但移除了搜索框和批次信息
- 显示扫码成功提示和条码信息
- 列出所有匹配的商品供用户选择

### 3. API数据格式扩展
- 扩展 `getScanResult` API的返回数据格式
- 新增 `multipleGoods` 字段用于返回一对多关系的商品列表

## 测试步骤

### 开发环境测试

1. **启动应用**
   ```bash
   npm run dev:mp-weixin
   ```

2. **进入扫码页面**
   - 导航到仓库管理 → 收货作业 → 扫码页面

3. **测试一对多关系**
   - 点击"测试扫码"按钮
   - 选择"01010101001 (一对多关系)"选项
   - 应该显示"扫码成功，请选择具体货品"提示
   - 自动跳转到货品确认页面

4. **验证货品确认页面**
   - 页面应显示扫码成功提示
   - 显示扫描的条码信息
   - 列出两个匹配的商品：
     - 53度郎酒红花郎(CH) | 650ml | 瓶
     - 53度郎酒红花郎(WC) | 650ml | 瓶
   - 每个商品右侧有"确认"按钮

5. **测试商品选择**
   - 点击任一商品的"确认"按钮
   - 应跳转到上架详情页面 (shelving-detail)
   - URL参数应包含选中的商品编码和条码信息

### 生产环境测试

1. **API接口配置**
   - 确保后端API支持返回 `multipleGoods` 字段
   - 当条码对应多个商品时，返回完整的商品列表

2. **真实数据测试**
   - 使用实际存在一对多关系的条码进行测试
   - 验证页面显示和跳转逻辑

## 页面对比

### 手动选择物料页面 (goods-select.vue)
- 包含搜索框
- 显示批次信息
- 从批次中加载所有货品

### 货品确认页面 (goods-confirm.vue)
- 无搜索框
- 无批次信息
- 显示扫码成功提示
- 显示条码信息
- 仅显示扫码匹配的商品

## 数据流程

```
扫码 → API验证 → 检查multipleGoods → 跳转确认页面 → 选择商品 → 上架详情
```

## 注意事项

1. **参数传递**：货品列表通过URL参数传递，需要进行JSON编码/解码
2. **页面注册**：新页面已在 `pages.json` 中注册
3. **兼容性**：保持与现有流程的兼容性，单一商品仍直接跳转
4. **错误处理**：添加了完善的错误处理和模拟数据支持

## 相关文件

- `pages/warehouse/receiving/scan.vue` - 扫码页面（已修改）
- `pages/warehouse/receiving/goods-confirm.vue` - 货品确认页面（新增）
- `api/warehouse.js` - API接口文档（已更新）
- `pages.json` - 页面配置（已更新）
