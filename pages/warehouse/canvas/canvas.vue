<template>
  <view>
    <canvas :canvas-id="canvasId" :style="{width: width+'px', height: height+'px'}"></canvas>
  </view>
</template>

<script>
export default {
  name: "StampCanvas",
  props: {
    text: { type: String, default: "待收货" },
    color: { type: String, default: "" },
    width: { type: Number, default: 125 },
    height: { type: Number, default: 100 }
  },
  computed: {
    // 生成唯一的canvas ID
    canvasId() {
      return 'stampCanvas_' + this._uid;
    },
    // 根据状态文字自动匹配颜色
    stampColor() {
      // 如果传入了color属性，优先使用传入的颜色
      if (this.color) {
        return this.color;
      }

      // 状态颜色映射表
      const statusColorMap = {
        '待收货': '#E99B47',
        '已收货': '#26B996',
        '待上架': '#E99B47',
        '已上架': '#2A9CFA',
        '待暂存': '#D94A55',
        '已暂存': '#26B996'
      };

      // 根据文字内容返回对应颜色，如果没有匹配则使用默认颜色
      return statusColorMap[this.text] || '#25b99a';
    }
  },
  mounted() {
    this.drawStamp();
  },
  watch: {
    text() {
      this.drawStamp();
    },
    color() {
      this.drawStamp();
    },
    stampColor() {
      this.drawStamp();
    }
  },
  methods: {
    drawStamp() {
      const ctx = uni.createCanvasContext(this.canvasId, this);

      const centerX = this.width / 2;
      const centerY = this.height / 2;
      const stampColor = this.stampColor;

      // ===== 外圈（粗） =====
      ctx.setLineWidth(2.2);
      ctx.setStrokeStyle(stampColor);
      ctx.beginPath();
      ctx.arc(centerX, centerY, 37, 0, 2 * Math.PI);
      ctx.stroke();

      // ===== 最外层细圈 =====
      ctx.setLineWidth(0.8);
      ctx.setStrokeStyle(stampColor);
      ctx.beginPath();
      ctx.arc(centerX, centerY, 40, 0, 2 * Math.PI);
      ctx.stroke();

      // ===== 内圈 =====
      ctx.setLineWidth(1.3);
      ctx.beginPath();
      ctx.arc(centerX, centerY, 29, 0, 2 * Math.PI);
      ctx.stroke();

      // ===== 最内层细圈 =====
      ctx.setLineWidth(0.7);
      ctx.setStrokeStyle(stampColor);
      ctx.beginPath();
      ctx.arc(centerX, centerY, 27, 0, 2 * Math.PI); // 内圈里面
      ctx.stroke();


      // ===== 倾斜胶带 =====
      ctx.save();
      ctx.translate(centerX, centerY);
      ctx.rotate(-20 * Math.PI / 180);

      const tapeWidth = 92;
      const tapeHeight = 16;
      const zigzagStep = 1.8;
      const zigzagOffset = 2.2;

      ctx.beginPath();

      // 左边锯齿
      ctx.moveTo(-tapeWidth/2, -tapeHeight/2);
      for (let y = -tapeHeight/2; y <= tapeHeight/2; y += zigzagStep) {
        const offset = (Math.random() * 2 - 1) * zigzagOffset;
        ctx.lineTo(-tapeWidth/2 + offset, y);
      }

      // 下边曲折
      for (let x = -tapeWidth/2; x <= tapeWidth/2; x += 3) {
        const offset = (Math.random() * 2 - 1) * 1.5;
        ctx.lineTo(x, tapeHeight/2 + offset);
      }

      // 右边锯齿
      for (let y = tapeHeight/2; y >= -tapeHeight/2; y -= zigzagStep) {
        const offset = (Math.random() * 2 - 1) * zigzagOffset;
        ctx.lineTo(tapeWidth/2 + offset, y);
      }

      // 上边曲折
      for (let x = tapeWidth/2; x >= -tapeWidth/2; x -= 3) {
        const offset = (Math.random() * 2 - 1) * 1.5;
        ctx.lineTo(x, -tapeHeight/2 + offset);
      }

      ctx.closePath();

      // 渐变填充
      const r = parseInt(stampColor.slice(1,3),16);
      const g = parseInt(stampColor.slice(3,5),16);
      const b = parseInt(stampColor.slice(5,7),16);

      const gradient = ctx.createLinearGradient(-tapeWidth/2, 0, tapeWidth/2, 0);
      gradient.addColorStop(0, `rgba(${r},${g},${b},0.12)`);
      gradient.addColorStop(0.5, `rgba(${r},${g},${b},0.18)`);
      gradient.addColorStop(1, `rgba(${r},${g},${b},0.12)`);

      ctx.setFillStyle(gradient);
      ctx.fill();

      // ===== 纤维纹理 =====
      for (let i = -tapeWidth/2; i < tapeWidth/2; i += 3) {
        ctx.setGlobalAlpha(Math.random() * 0.15 + 0.05);
        ctx.setFillStyle(stampColor);
        ctx.fillRect(i, -tapeHeight/2, 1, tapeHeight);
      }
      ctx.setGlobalAlpha(1);

      // ===== 文字 =====
      ctx.setFillStyle(stampColor);
      ctx.setFontSize(14);
      ctx.setTextAlign('center');
      ctx.setTextBaseline('middle');
      ctx.font = 'bold 14px sans-serif';
      ctx.fillText(this.text, 0, 0);

      ctx.restore();

      ctx.draw();
    }
  }
};
</script>
