<template>
  <view class="goods-confirm-container">
    <!-- 顶部标题栏 -->
    <view class="header">
      <view class="back-btn" @click="goBack">
        <uni-icons type="left" size="20" color="#333"></uni-icons>
      </view>
      <view class="title">选择货品</view>
      <view class="placeholder"></view>
    </view>

    <!-- 货品列表 -->
    <view class="goods-list">
      <view 
        v-for="(goods, index) in goodsList" 
        :key="index"
        class="goods-item"
        @click="selectGoods(goods)"
      >
        <!-- 货品基本信息 -->
        <view class="goods-info">
          <view class="goods-details">
            <view class="detail-row">
              <text class="label">物料编码：</text>
              <text class="value">{{ goods.goodsCode }}</text>
            </view>
            <view class="detail-row">
              <text class="label">商品名：</text>
              <text class="value">{{ goods.goodsName }}</text>
            </view>
          </view>
        </view>

        <!-- 右侧确认按钮 -->
        <view class="confirm-btn" @click.stop="confirmSelect(goods)">
          <text class="confirm-text">确认</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="goodsList.length === 0" class="empty-state">
      <view class="empty-icon">
        <uni-icons type="info" size="80" color="#ccc"></uni-icons>
      </view>
      <text class="empty-text">暂无货品信息</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 页面参数
      taskId: '',
      batchNumber: '',
      barcode: '',
      goodsList: []
    }
  },
  
  onLoad(options) {
    // 获取页面参数
    this.taskId = options.taskId || ''
    this.batchNumber = options.batchNumber || ''
    this.barcode = options.barcode || ''
    
    // 解析货品列表
    if (options.goodsList) {
      try {
        this.goodsList = JSON.parse(decodeURIComponent(options.goodsList))
      } catch (error) {
        console.error('解析货品列表失败:', error)
        this.goodsList = []
      }
    }
    
    console.log('货品确认页面参数:', { 
      taskId: this.taskId, 
      batchNumber: this.batchNumber,
      barcode: this.barcode,
      goodsList: this.goodsList
    })
  },
  
  methods: {
    // 选择货品（点击整行）
    selectGoods(goods) {
      // 点击整行时不执行任何操作，只有点击确认按钮才选择
    },

    // 确认选择货品
    confirmSelect(goods) {
      console.log('确认选择货品:', goods)

      // 跳转到上架详情页面
      uni.navigateTo({
        url: `/pages/warehouse/shelving/shelving-detail?taskId=${this.taskId}&goodsCode=${goods.goodsCode}&mode=shelving&from=scan&barcode=${this.barcode}`
      })
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.goods-confirm-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部标题栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.placeholder {
  width: 60rpx;
}

/* 扫码信息提示 */
.scan-info {
  background-color: #fff;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
}

.scan-tip {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.tip-text {
  font-size: 28rpx;
  color: #4CD964;
  font-weight: 500;
}

.barcode-info {
  display: flex;
  align-items: center;
}

.barcode-label {
  font-size: 28rpx;
  color: #666;
}

.barcode-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

/* 货品列表 */
.goods-list {
  padding: 0 24rpx;
}

.goods-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.goods-info {
  flex: 1;
}

.goods-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-row {
  display: flex;
  align-items: center;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.confirm-btn {
  background-color: #007AFF;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  margin-left: 24rpx;
}

.confirm-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
