<template>
  <view class="scan-container">
    <!-- 顶部标题栏 -->
    <view class="header">
      <view class="back-btn" @click="goBack">
        <uni-icons type="left" size="20" color="#333"></uni-icons>
      </view>
      <view class="title">扫一扫</view>
      <view class="placeholder"></view>
    </view>

    <!-- 扫码提示文字 -->
    <view class="scan-tip">
      <text>扫描货品条码</text>
    </view>

    <!-- 扫码区域 -->
    <view class="scan-area">
      <!-- 扫码框 -->
      <view class="scan-frame">
        <!-- 四个角的装饰 -->
        <view class="corner corner-top-left"></view>
        <view class="corner corner-top-right"></view>
        <view class="corner corner-bottom-left"></view>
        <view class="corner corner-bottom-right"></view>
        
        <!-- 扫描线动画 -->
        <view class="scan-line" :class="{ scanning: isScanning }"></view>
        
        <!-- 条码显示区域 -->
        <view v-if="scannedCode" class="barcode-display">
          <!-- 条码样式显示 -->
          <view class="barcode-visual">
            <view class="barcode-lines">
              <!-- 模拟数据：条码线条 -->
              <view class="barcode-line" style="width: 2rpx;"></view>
              <view class="barcode-line" style="width: 1rpx;"></view>
              <view class="barcode-line" style="width: 3rpx;"></view>
              <view class="barcode-line" style="width: 2rpx;"></view>
              <view class="barcode-line" style="width: 1rpx;"></view>
              <view class="barcode-line" style="width: 2rpx;"></view>
              <view class="barcode-line" style="width: 4rpx;"></view>
              <view class="barcode-line" style="width: 1rpx;"></view>
              <view class="barcode-line" style="width: 2rpx;"></view>
              <view class="barcode-line" style="width: 3rpx;"></view>
              <view class="barcode-line" style="width: 1rpx;"></view>
              <view class="barcode-line" style="width: 2rpx;"></view>
              <view class="barcode-line" style="width: 1rpx;"></view>
              <view class="barcode-line" style="width: 3rpx;"></view>
              <view class="barcode-line" style="width: 2rpx;"></view>
              <view class="barcode-line" style="width: 1rpx;"></view>
              <view class="barcode-line" style="width: 4rpx;"></view>
              <view class="barcode-line" style="width: 2rpx;"></view>
              <view class="barcode-line" style="width: 1rpx;"></view>
              <view class="barcode-line" style="width: 3rpx;"></view>
            </view>
          </view>
          <text class="barcode-number">{{ scannedCode }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-buttons">
      <!-- 开始扫码按钮 -->
      <button class="scan-btn" @click="startScan" :disabled="isScanning">
        <uni-icons type="camera" size="20" color="#fff"></uni-icons>
        <text>{{ isScanning ? '扫码中...' : '开始扫码' }}</text>
      </button>

      <!-- 开发环境测试按钮 -->
      <button v-if="isDev" class="test-btn" @click="testScan">
        <uni-icons type="compose" size="20" color="#FF9500"></uni-icons>
        <text>测试扫码</text>
      </button>

      <!-- 手动选择按钮 -->
      <button class="manual-btn" @click="manualSelect">
        <uni-icons type="list" size="20" color="#007AFF"></uni-icons>
        <text>手动选择物料</text>
      </button>
    </view>

    <!-- 扫码结果提示 -->
    <view v-if="scanResult" class="scan-result">
      <view class="result-item" :class="scanResult.type">
        <view class="result-icon">
          <uni-icons :type="scanResult.type === 'success' ? 'checkmarkempty' : 'info'" 
                     :color="scanResult.type === 'success' ? '#4CD964' : '#FF9500'" 
                     size="16"></uni-icons>
        </view>
        <text class="result-text">{{ scanResult.message }}</text>
      </view>
    </view>


    <!-- 超收货品确认弹窗 -->
    <uni-popup ref="overReceivePopup" type="dialog">
      <uni-popup-dialog
        type="warn"
        title="超收货品提示"
        :content="overReceiveMessage"
        :before-close="true"
        @close="closeOverReceiveDialog"
        @confirm="confirmOverReceive"
        @cancel="cancelOverReceive">
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import { getScanResult } from '@/api/warehouse'

export default {
  data() {
    return {
      // 页面参数
      taskId: '',
      batchNumber: '',
      
      // 扫码状态
      isScanning: false,
      scannedCode: '',
      scanResult: null,
      
      // 扫码结果数据
      goodsInfo: null,

      // 超收货品相关
      overReceiveMessage: '',
      pendingOverReceiveCode: '', // 待确认的超收货品编码

      // 开发环境标识
      isDev: process.env.NODE_ENV === 'development'
    }
  },
  
  onLoad(options) {
    // 获取页面参数
    this.taskId = options.taskId || ''
    this.batchNumber = options.batchNumber || ''

    console.log('收货扫码页面参数:', { taskId: this.taskId, batchNumber: this.batchNumber })
  },

  onShow() {
    // 页面显示时不自动扫码，等待用户点击按钮
    console.log('扫码页面显示')
  },
  
  onHide() {
    // 页面隐藏时停止扫码
    this.stopScan()
  },
  
  methods: {
    
    // 开始扫码
    startScan() {
      console.log('开始扫码...')
      this.isScanning = true

      // 调用uni-app扫码API
      uni.scanCode({
        success: (res) => {
          console.log('扫码成功:', res)
          this.handleScanResult(res.result)
        },
        fail: (error) => {
          console.error('扫码失败:', error)
          this.isScanning = false

          if (error.errMsg && error.errMsg.includes('cancel')) {
            // 用户取消扫码
            console.log('用户取消扫码')
            uni.showToast({
              title: '已取消扫码',
              icon: 'none'
            })
          } else {
            console.error('扫码错误详情:', error)
            uni.showToast({
              title: '扫码失败，请重试',
              icon: 'error'
            })
          }
        },
        complete: () => {
          console.log('扫码操作完成')
        }
      })
    },
    
    // 停止扫码
    stopScan() {
      this.isScanning = false
    },

    // 测试扫码（开发环境）
    testScan() {
      console.log('测试扫码功能 - 当前批次编号:', this.batchNumber)

      // 显示测试码选择弹窗
      uni.showActionSheet({
        itemList: [
          '6856236585695 (属于当前批次)',
          '6856236585696 (属于当前批次)',
          '01010101001 (一对多关系)',
          '9999999999999 (不属于当前批次)',
          '8888888888888 (不属于当前批次)',
          '1234567890123 (随机测试码)',
          '查看调试信息'
        ],
        success: (res) => {
          const testCodes = [
            '6856236585695',
            '6856236585696',
            '01010101001',
            '9999999999999',
            '8888888888888',
            '1234567890123'
          ]

          // 如果选择查看调试信息
          if (res.tapIndex === 6) {
            this.showDebugInfo()
            return
          }

          const selectedCode = testCodes[res.tapIndex]

          uni.showToast({
            title: `模拟扫码: ${selectedCode}`,
            icon: 'none',
            duration: 1500
          })

          setTimeout(() => {
            console.log('开始处理测试扫码结果:', selectedCode)
            this.handleScanResult(selectedCode)
          }, 1500)
        },
        fail: () => {
          console.log('用户取消选择测试码')
        }
      })
    },

    // 显示调试信息
    showDebugInfo() {
      const debugInfo = `
调试信息:
- 任务ID: ${this.taskId}
- 批次编号: ${this.batchNumber}
- 环境: ${process.env.NODE_ENV}
- 当前时间: ${new Date().toLocaleString()}
      `

      uni.showModal({
        title: '调试信息',
        content: debugInfo,
        showCancel: false,
        confirmText: '确定'
      })
    },
    
    // 处理扫码结果
    async handleScanResult(code) {
      this.scannedCode = code
      this.isScanning = false

      // 检查网络状态
      const networkStatus = await this.checkNetworkStatus()
      console.log('网络状态检查:', networkStatus)

      if (!networkStatus.isConnected && process.env.NODE_ENV !== 'development') {
        this.showScanError('网络未连接，请检查网络设置')
        return
      }

      try {
        // 【后端接口】验证货品条码是否属于当前批次
        const validateResponse = await this.validateGoodsCode(code)

        // 检查是否存在多个匹配的商品（一对多情况）
        if (validateResponse.multipleGoods && validateResponse.multipleGoods.length > 1) {
          // 存在一对多关系，跳转到货品选择页面
          this.showScanSuccess('扫码成功，请选择具体货品')

          setTimeout(() => {
            uni.navigateTo({
              url: `/pages/warehouse/receiving/goods-confirm?taskId=${this.taskId}&batchNumber=${this.batchNumber}&barcode=${code}&goodsList=${encodeURIComponent(JSON.stringify(validateResponse.multipleGoods))}`
            })
          }, 1500)

        } else if (validateResponse.belongsToBatch) {
          // 属于当前批次，直接跳转到上架页面
          this.showScanSuccess('扫码成功，跳转到上架作业')

          setTimeout(() => {
            uni.navigateTo({
              url: `/pages/warehouse/shelving/shelving-detail?taskId=${this.taskId}&goodsCode=${code}&mode=shelving&belongsToCurrentBatch=true`
            })
          }, 1500)

        } else {
          // 不属于当前批次，显示超收确认弹窗
          this.pendingOverReceiveCode = code
          this.overReceiveMessage = `非本批次下的收货商品，是否继续收货？`
          this.showOverReceiveDialog()
        }

      } catch (error) {
        console.error('处理扫码结果失败:', error)

        // 根据错误类型显示不同的提示信息
        let errorMessage = '扫码验证失败，请重试'

        if (error.message) {
          if (error.message.includes('网络') || error.message.includes('连接') || error.message.includes('timeout')) {
            errorMessage = '网络连接异常，请检查网络后重试'
          } else if (error.message.includes('验证失败')) {
            errorMessage = '货品验证失败，请确认条码是否正确'
          } else {
            errorMessage = error.message
          }
        }

        // 开发环境使用模拟数据进行演示
        if (process.env.NODE_ENV === 'development') {
          console.log('开发环境：API调用失败，使用模拟数据')

          // 使用模拟验证结果
          try {
            const mockResult = this.getMockValidationResult(code)

            // 检查是否存在多个匹配的商品（模拟一对多情况）
            if (mockResult.multipleGoods && mockResult.multipleGoods.length > 1) {
              this.showScanSuccess('扫码成功，请选择具体货品（模拟数据）')

              setTimeout(() => {
                uni.navigateTo({
                  url: `/pages/warehouse/receiving/goods-confirm?taskId=${this.taskId}&batchNumber=${this.batchNumber}&barcode=${code}&goodsList=${encodeURIComponent(JSON.stringify(mockResult.multipleGoods))}`
                })
              }, 1500)

            } else if (mockResult.belongsToBatch) {
              // 模拟属于当前批次的情况
              this.showScanSuccess('扫码成功，跳转到上架作业（模拟数据）')

              setTimeout(() => {
                uni.navigateTo({
                  url: `/pages/warehouse/shelving/shelving-detail?taskId=${this.taskId}&goodsCode=${code}&mode=shelving&belongsToCurrentBatch=true`
                })
              }, 1500)
            } else {
              // 模拟不属于当前批次的情况
              this.pendingOverReceiveCode = code
              this.overReceiveMessage = `非本批次下的收货商品，是否继续收货？（模拟数据）`
              this.showOverReceiveDialog()
            }
          } catch (mockError) {
            console.error('模拟数据处理失败:', mockError)
            this.showScanError(errorMessage)
          }
        } else {
          this.showScanError(errorMessage)
        }
      }
    },

    // 【后端接口】验证货品条码是否属于当前批次
    async validateGoodsCode(code) {
      try {
        console.log('开始验证货品条码:', code, '批次编号:', this.batchNumber)

        // 调用验证接口，传递批次编号用于验证
        const response = await getScanResult(code, 'goods', this.batchNumber)

        console.log('API响应:', response)

        if (response && response.code === 200) {
          return {
            belongsToBatch: response.data.belongsToBatch || false,
            goodsName: response.data.goodsName || '',
            goodsCode: code
          }
        } else {
          const errorMsg = response?.msg || '验证失败'
          console.error('API返回错误:', errorMsg, '完整响应:', response)
          throw new Error(errorMsg)
        }
      } catch (apiError) {
        console.error('API调用异常:', apiError)

        // 在开发环境下，如果API调用失败，使用模拟数据
        if (process.env.NODE_ENV === 'development') {
          console.log('开发环境：使用模拟数据进行验证')
          return this.getMockValidationResult(code)
        }

        throw apiError
      }
    },

    // 获取模拟验证结果（开发环境使用）
    getMockValidationResult(code) {
      // 模拟当前批次的货品条码
      const currentBatchCodes = [
        '6856236585695',
        '6856236585696'
      ]

      // 模拟一对多关系的条码（一个条码对应多个商品编码）
      const multipleGoodsCodes = [
        '01010101001' // 这个条码对应多个商品
      ]

      const belongsToBatch = currentBatchCodes.includes(code)
      const hasMultipleGoods = multipleGoodsCodes.includes(code)

      if (hasMultipleGoods) {
        // 模拟一对多的情况
        return {
          belongsToBatch: true,
          multipleGoods: [
            {
              goodsCode: '01010101001',
              goodsName: '53度郎酒红花郎(CH) | 650ml | 瓶'
            },
            {
              goodsCode: '01010101001',
              goodsName: '53度郎酒红花郎(WC) | 650ml | 瓶'
            }
          ]
        }
      }

      return {
        belongsToBatch: belongsToBatch,
        goodsName: belongsToBatch ? '测试商品A' : '其他批次商品',
        goodsCode: code
      }
    },

    // 检查网络状态
    checkNetworkStatus() {
      return new Promise((resolve) => {
        uni.getNetworkType({
          success: (res) => {
            console.log('网络类型:', res.networkType)
            const isConnected = res.networkType !== 'none'
            resolve({
              isConnected: isConnected,
              networkType: res.networkType
            })
          },
          fail: () => {
            console.log('获取网络状态失败')
            resolve({
              isConnected: false,
              networkType: 'unknown'
            })
          }
        })
      })
    },
    
    // 显示扫码成功提示
    showScanSuccess(message) {
      this.scanResult = {
        type: 'success',
        message: message
      }
      
      // 3秒后清除提示
      setTimeout(() => {
        this.scanResult = null
      }, 3000)
    },
    
    // 显示扫码错误提示
    showScanError(message) {
      this.scanResult = {
        type: 'error',
        message: message
      }
      
      // 3秒后清除提示并重新开始扫码
      setTimeout(() => {
        this.scanResult = null
        this.scannedCode = ''
        this.startScan()
      }, 3000)
    },
    
    // 手动选择物料
    manualSelect() {
      // 【功能扩展】跳转到物料选择页面
      uni.navigateTo({
        url: `/pages/warehouse/receiving/goods-select?taskId=${this.taskId}&batchNumber=${this.batchNumber}`
      })
    },

    // 显示超收确认弹窗
    showOverReceiveDialog() {
      this.$refs.overReceivePopup.open()
    },

    // 关闭超收确认弹窗
    closeOverReceiveDialog() {
      this.$refs.overReceivePopup.close()
      this.pendingOverReceiveCode = ''
      this.overReceiveMessage = ''
    },

    // 确认超收货品收货
    confirmOverReceive() {
      const code = this.pendingOverReceiveCode

      this.closeOverReceiveDialog()
      this.showScanSuccess('确认收货，跳转到上架作业')

      // 跳转到上架详情页面
      setTimeout(() => {
        uni.navigateTo({
          url: `/pages/warehouse/shelving/shelving-detail?taskId=${this.taskId}&goodsCode=${code}&mode=shelving&from=overReceive&belongsToCurrentBatch=false`
        })
      }, 1500)
    },

    // 取消超收货品收货
    cancelOverReceive() {
      this.closeOverReceiveDialog()

      // 重新开始扫码
      setTimeout(() => {
        this.startScan()
      }, 500)
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.scan-container {
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
  min-height: 100vh;
  position: relative;
}

/* 顶部标题栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.placeholder {
  width: 60rpx;
}

/* 扫码提示 */
.scan-tip {
  text-align: center;
  padding: 60rpx 0 40rpx;
  
  text {
    font-size: 32rpx;
    color: #666;
  }
}

/* 扫码区域 */
.scan-area {
  display: flex;
  justify-content: center;
  padding: 0 60rpx;
  margin-bottom: 80rpx;
}

.scan-frame {
  width: 500rpx;
  height: 500rpx;
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid #007AFF;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 四个角的装饰 */
.corner {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border: 6rpx solid #007AFF;
}

.corner-top-left {
  top: -2rpx;
  left: -2rpx;
  border-right: none;
  border-bottom: none;
  border-top-left-radius: 12rpx;
}

.corner-top-right {
  top: -2rpx;
  right: -2rpx;
  border-left: none;
  border-bottom: none;
  border-top-right-radius: 12rpx;
}

.corner-bottom-left {
  bottom: -2rpx;
  left: -2rpx;
  border-right: none;
  border-top: none;
  border-bottom-left-radius: 12rpx;
}

.corner-bottom-right {
  bottom: -2rpx;
  right: -2rpx;
  border-left: none;
  border-top: none;
  border-bottom-right-radius: 12rpx;
}

/* 扫描线动画 */
.scan-line {
  position: absolute;
  top: 0;
  left: 10rpx;
  right: 10rpx;
  height: 4rpx;
  background: linear-gradient(90deg, transparent, #007AFF, transparent);
  border-radius: 2rpx;
  opacity: 0;
}

.scan-line.scanning {
  animation: scanAnimation 2s linear infinite;
  opacity: 1;
}

@keyframes scanAnimation {
  0% {
    top: 10rpx;
  }
  100% {
    top: 480rpx;
  }
}

/* 条码显示 */
.barcode-display {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.barcode-visual {
  width: 300rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.barcode-lines {
  display: flex;
  align-items: center;
  gap: 2rpx;
  height: 80rpx;
}

.barcode-line {
  background-color: #000;
  height: 100%;
  min-width: 1rpx;
}

.barcode-number {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

/* 操作按钮区域 */
.action-buttons {
  padding: 0 60rpx;
  margin-bottom: 60rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.scan-btn {
  width: 100%;
  height: 88rpx;
  background-color: #007AFF;
  border: none;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;

  &:disabled {
    background-color: #ccc;
  }

  text {
    font-size: 32rpx;
    color: #fff;
    font-weight: 500;
  }
}

.test-btn {
  width: 100%;
  height: 88rpx;
  background-color: #FF9500;
  border: none;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;

  text {
    font-size: 32rpx;
    color: #fff;
    font-weight: 500;
  }
}

.manual-btn {
  width: 100%;
  height: 88rpx;
  background-color: #fff;
  border: 2rpx solid #007AFF;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;

  text {
    font-size: 32rpx;
    color: #007AFF;
    font-weight: 500;
  }
}

/* 扫码结果提示 */
.scan-result {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.result-item {
  background-color: rgba(0, 0, 0, 0.8);
  padding: 24rpx 40rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  
  &.success {
    background-color: rgba(76, 217, 100, 0.9);
  }
  
  &.error {
    background-color: rgba(255, 149, 0, 0.9);
  }
}

.result-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}


</style>
