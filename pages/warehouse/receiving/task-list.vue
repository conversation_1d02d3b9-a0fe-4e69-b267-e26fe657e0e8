<template>
  <view class="container">
    <!-- 顶部标签栏 -->
    <view class="tab-bar">
      <view
        class="tab-item"
        :class="{ active: currentStatus === '' }"
        @click="switchTab('')"
      >
        <text class="tab-text">全部</text>
      </view>
      <view
        class="tab-item"
        :class="{ active: currentStatus === 'pending' }"
        @click="switchTab('pending')"
      >
        <text class="tab-text">待收货</text>
      </view>
      <view
        class="tab-item"
        :class="{ active: currentStatus === 'completed' }"
        @click="switchTab('completed')"
      >
        <text class="tab-text">已收货</text>
      </view>
    </view>

    <!-- 任务列表 -->
    <view class="task-list">
      <view
        v-for="task in taskList"
        :key="task.id"
        class="task-item"
      >
        <!-- 任务基本信息 -->
        <view class="task-header">
          <view class="task-title">批次编号</view>
          <view class="task-number">{{ task.batchNumber }}</view>
        </view>

        <!-- 分隔线 -->
        <view class="divider-line"></view>

        <view class="task-content">
          <view class="task-info">
            <view class="info-row full-width">
              <text class="label">车牌号</text>
              <text class="value">{{ task.purchaseOrderNo }}</text>
            </view>
            <view class="info-row full-width">
              <text class="label">月台号</text>
              <text class="value">{{ task.platformNo }}</text>
            </view>
            <view class="info-row full-width">
              <text class="label">品类数量</text>
              <text class="value">{{ task.goodsCount }}</text>
            </view>
            <view class="info-row full-width">
              <text class="label">送货单位</text>
              <text class="value">{{ task.deliveryCompany }}</text>
            </view>
          </view>

          <!-- 右上角状态印章 -->
          <view class="stamp-container">
            <StampCanvas :text="getStatusText(task.status)" />
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="task-actions">
          <button v-if="task.status === 'pending'" class="btn btn-receive" @click.stop="startReceiving(task)">
            <uni-icons type="compose" size="20"></uni-icons>
            <text>收货</text>
          </button>
          <button class="btn btn-view" @click.stop="viewDetail(task)">
            <uni-icons type="eye" size="20"></uni-icons>
            <text>查看</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="taskList.length === 0 && !loading" class="empty-state">
      <image src="/static/images/empty.png" class="empty-image"></image>
      <text class="empty-text">{{ getEmptyText() }}</text>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <uni-load-more status="loading" content-text="加载中..."></uni-load-more>
    </view>

    <!-- 加载更多 -->
    <view v-if="hasMore" class="load-more" @click="loadMore">
      <text>加载更多</text>
    </view>
  </view>
</template>

<script>
import StampCanvas from "../canvas/canvas.vue"
import { getReceivingTaskList } from '@/api/warehouse'

export default {
  components: {StampCanvas},
  data() {
    return {
      // 任务列表数据
      allTaskList: [ // 保存所有任务数据
        {
          id: '1',
          batchNumber: '20152368652',
          purchaseOrderNo: '川A.8562365',
          platformNo: '526',
          goodsCount: 4,
          deliveryCompany: '五粮液集团(股份)公司',
          status: 'pending'
        },
        {
          id: '2',
          batchNumber: '20152368653',
          purchaseOrderNo: '川A.8562366',
          platformNo: '527',
          goodsCount: 6,
          deliveryCompany: '五粮液集团(股份)公司',
          status: 'completed'
        },
        {
          id: '3',
          batchNumber: '20152368654',
          purchaseOrderNo: '川A.8562367',
          platformNo: '528',
          goodsCount: 8,
          deliveryCompany: '五粮液集团(股份)公司',
          status: 'completed'
        },
        {
          id: '4',
          batchNumber: '20152368655',
          purchaseOrderNo: '川A.8562368',
          platformNo: '529',
          goodsCount: 3,
          deliveryCompany: '茅台集团(股份)公司',
          status: 'pending'
        },
        {
          id: '5',
          batchNumber: '20152368656',
          purchaseOrderNo: '川A.8562369',
          platformNo: '530',
          goodsCount: 5,
          deliveryCompany: '剑南春集团公司',
          status: 'completed'
        }
      ],
      taskList: [], // 当前显示的任务列表（经过筛选）
      hasMore: true,
      loading: false,
      currentStatus: '', // 当前选中的状态：'' 全部, 'pending' 待收货, 'completed' 已收货
      currentPage: 1,
      pageSize: 10
    }
  },
  
  onLoad() {
    this.loadTaskList()
  },

  computed: {
    // 根据状态筛选任务列表
    filteredTaskList() {
      if (!this.currentStatus) {
        return this.allTaskList // 显示全部
      }
      return this.allTaskList.filter(task => task.status === this.currentStatus)
    },

    // 获取各状态的任务数量
    statusCounts() {
      const counts = {
        all: this.allTaskList.length,
        pending: this.allTaskList.filter(task => task.status === 'pending').length,
        completed: this.allTaskList.filter(task => task.status === 'completed').length
      }
      return counts
    }
  },
  
  methods: {
    // 【后端接口】加载任务列表数据
    async loadTaskList() {
      try {
        this.loading = true

        // 【真实接口调用】
        const response = await getReceivingTaskList({
          pageNum: 1,
          pageSize: 50, // 增加页面大小以获取更多数据用于筛选
          status: this.currentStatus || undefined // 不传status参数获取全部数据
        })

        if (response.code === 200) {
          this.allTaskList = response.rows || []
          this.applyFilter() // 应用筛选
          this.hasMore = response.total > this.allTaskList.length
        } else {
          throw new Error(response.msg || '获取数据失败')
        }

        console.log('加载任务列表成功，总数:', this.allTaskList.length)

      } catch (error) {
        console.error('加载任务列表失败:', error)

        // 如果是开发环境或接口未实现，使用模拟数据
        if (process.env.NODE_ENV === 'development' || error.message.includes('404')) {
          console.log('使用模拟数据')
          // 模拟数据已在data中定义，直接应用筛选
          this.applyFilter()
        } else {
          uni.showToast({
            title: '加载失败',
            icon: 'error'
          })
        }
      } finally {
        this.loading = false
      }
    },

    // 应用筛选逻辑
    applyFilter() {
      if (!this.currentStatus) {
        // 显示全部
        this.taskList = [...this.allTaskList]
      } else {
        // 根据状态筛选
        this.taskList = this.allTaskList.filter(task => task.status === this.currentStatus)
      }

      console.log(`筛选结果: ${this.currentStatus || '全部'} - ${this.taskList.length}条`)
    },

    // 【后端接口】加载更多数据
    async loadMore() {
      if (this.loading) return
      try {
        this.loading = true
        // 【接口地址】GET /warehouse/receiving/task/list
        // 【请求参数】{ pageNum: this.currentPage + 1, pageSize: 10 }
        console.log('加载更多任务')
      } catch (error) {
        console.error('加载更多失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待收货',
        'completed': '已收货'
      }
      return statusMap[status] || '待收货';
    },

    // 获取空状态文本
    getEmptyText() {
      const emptyTextMap = {
        '': '暂无任务',
        'pending': '暂无待收货任务',
        'completed': '暂无已收货任务'
      }
      return emptyTextMap[this.currentStatus] || '暂无任务';
    },

    // 开始收货 - 跳转到扫一扫页面
    startReceiving(task) {
      // 跳转到扫一扫页面进行收货操作
      uni.navigateTo({
        url: `/pages/warehouse/receiving/scan?taskId=${task.id}&batchNumber=${task.batchNumber}`
      })
    },

    // 查看详情
    viewDetail(task) {
      uni.navigateTo({
        url: `/pages/warehouse/receiving/receiving-detail?taskId=${task.id}&mode=view`
      })
    },

    // 切换标签
    switchTab(status) {
      if (this.currentStatus === status) return

      console.log('切换标签:', status || '全部')
      this.currentStatus = status
      this.currentPage = 1

      // 直接应用筛选，无需重新加载数据
      this.applyFilter()

      // 显示切换提示
      const statusText = status === 'pending' ? '待收货' : status === 'completed' ? '已收货' : '全部'
      uni.showToast({
        title: `已切换到${statusText}`,
        icon: 'none',
        duration: 1000
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部标签栏 */
.tab-bar {
  display: flex;
  background-color: #fff;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  font-size: 32rpx;
  color: #666;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;

  &.active {
    color: #07c160;
    font-weight: 500;

    .tab-count {
      color: #07c160;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 4rpx;
      background-color: #07c160;
      border-radius: 2rpx;
    }
  }
}

.tab-text {
  font-size: 32rpx;
  font-weight: inherit;
}

.tab-count {
  font-size: 24rpx;
  color: #999;
  font-weight: 400;
}

/* 任务列表 */
.task-list {
  padding: 16rpx 24rpx;
}

.task-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 任务头部 */
.task-header {
  margin-bottom: 12rpx;
}

.task-title {
  font-size: 22rpx;
  color: #666;
  font-weight: 400;
  margin-bottom: 2rpx;
}

.task-number {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}

/* 任务内容区域 */
.task-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
  position: relative;
}

.task-info {
  flex: 1;
}

.info-row {
  display: flex;
  margin-bottom: 8rpx;
  align-items: center;

  &:last-child {
    margin-bottom: 0;
  }

  &.full-width {
    .label {
      width: 120rpx;
    }
    .value {
      flex: 1;
    }
  }
}

.label {
  font-size: 26rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

.value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 分隔线 */
.divider-line {
  height: 1rpx;
  background-color: #e5e5e5;
  margin: 12rpx 0;
  width: calc(100% - 140px); /* 从左边到印章左边，考虑印章宽度125px + 一些间距 */
}

/* 印章容器 */
.stamp-container {
  position: absolute;
  top: -110rpx;
  right: -20rpx;
  z-index: 10;
}

/* 操作按钮 */
.task-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  margin-top: 16rpx;
  padding-right: 30rpx;
}

.task-item .task-actions button.btn {
  flex: none;
  width: auto;
  min-width: 120rpx;
  max-width: 150rpx;
  height: 56rpx;
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  font-size: 24rpx;
  border: 1rpx solid #e5e5e5;
  background-color: #fff;
  color: #333;
  padding: 0 20rpx;
  margin: 0;
  line-height: normal;
  box-sizing: border-box;
}

.task-item .task-actions button.btn.btn-receive uni-icons {
  color: #ff6b35;
}

.task-item .task-actions button.btn.btn-view uni-icons {
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  padding: 60rpx 0;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 32rpx;
  color: #666;
  font-size: 28rpx;
}
</style>